#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试保存功能
"""

import os
import sys
import json

# 添加backend目录到路径
sys.path.append('backend')

from history_manager import HistoryManager

def debug_save():
    """调试保存功能"""
    print("=== 调试保存功能 ===\n")
    
    # 创建历史管理器
    history_manager = HistoryManager()
    
    print(f"历史目录: {history_manager.history_dir}")
    print(f"历史文件: {history_manager.history_file}")
    print(f"仿真结果目录: {history_manager.simulation_results_dir}")
    
    # 简单的测试配置
    test_config = {
        "network_config": {
            "type": "predefined",
            "entrance_plan": "仅开放东侧出入口"
        },
        "signal_config": {
            "type": "predefined"
        },
        "traffic_config": {
            "type": "predefined",
            "scenario": "进场"
        }
    }
    
    print("\n开始保存测试...")
    
    try:
        result = history_manager.save_scheme(
            config=test_config,
            name="调试测试方案",
            description="用于调试的测试方案",
            simulation_results=None
        )
        
        print(f"保存结果: {result}")
        
        if result['success']:
            scheme_id = result['scheme_id']
            scheme_file = result['scheme_file']
            
            print(f"\n方案ID: {scheme_id}")
            print(f"方案文件: {scheme_file}")
            
            # 检查文件是否存在
            if os.path.exists(scheme_file):
                print(f"✅ 历史文件存在: {scheme_file}")
                with open(scheme_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"文件内容预览: simulation_id={data.get('simulation_id')}")
            else:
                print(f"❌ 历史文件不存在: {scheme_file}")
            
            # 检查activity文件
            activity_file = os.path.join('backend/sumo/output/2023/activity', f'simulation_result_{scheme_id}.json')
            if os.path.exists(activity_file):
                print(f"✅ 项目方文件存在: {activity_file}")
            else:
                print(f"❌ 项目方文件不存在: {activity_file}")
                
    except Exception as e:
        print(f"❌ 保存过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_save()
