<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终测试 - vw/vh 方案验证</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        /* 调高右侧图表的高度 */
        .chart-container {
            height: 15vh !important;
            min-height: 12vh !important;
            max-height: 25vh !important;
        }
        
        /* 调高右侧路段分析图表的高度 */
        .edge-chart-container {
            height: 18vh !important;
            min-height: 15vh !important;
        }
        
        /* 确保charts-section容器可以适应更高的图表 */
        .charts-container {
            max-height: 90vh !important;
            overflow-y: auto;
        }
        
        /* 调整每个结果卡片之间的间距 */
        .result-card {
            margin-bottom: 1.5vh !important;
        }

        body {
            background: #05202e;
            color: #95D7E3;
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }

        .test-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.8);
            z-index: 9999;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
        }

        .test-info {
            background: rgba(48, 171, 232, 0.1);
            border: 0.2vw solid #30abe8;
            border-radius: 1vw;
            padding: 2vh 3vw;
            text-align: center;
            max-width: 60vw;
        }

        .test-info h1 {
            font-size: 2vw;
            color: #30abe8;
            margin-bottom: 1vh;
        }

        .test-info p {
            font-size: 1vw;
            margin-bottom: 1vh;
            line-height: 1.5;
        }

        .zoom-display {
            background: rgba(255, 198, 25, 0.2);
            border: 0.1vw solid #ffc619;
            border-radius: 0.5vw;
            padding: 1vh 2vw;
            margin: 1vh 0;
            font-size: 1.2vw;
            color: #ffc619;
            font-weight: bold;
        }

        .test-buttons {
            display: flex;
            gap: 1vw;
            margin-top: 2vh;
        }

        .test-btn {
            background: transparent;
            color: #30abe8;
            border: 0.1vw solid #30abe8;
            border-radius: 0.5vw;
            padding: 1vh 2vw;
            font-size: 0.9vw;
            cursor: pointer;
            transition: all 0.3s;
        }

        .test-btn:hover {
            background: rgba(48, 171, 232, 0.2);
        }

        .test-btn.primary {
            background: #30abe8;
            color: white;
        }

        .test-btn.primary:hover {
            background: #2a7dc9;
        }

        .instructions {
            margin-top: 2vh;
            font-size: 0.8vw;
            color: #aaaaaa;
            text-align: left;
        }

        .instructions ul {
            list-style: none;
            padding-left: 1vw;
        }

        .instructions li {
            margin-bottom: 0.5vh;
        }

        .instructions li:before {
            content: "✓ ";
            color: #4CAF50;
            font-weight: bold;
        }

        /* 隐藏的主页面 */
        .main-page {
            opacity: 0.3;
            pointer-events: none;
        }

        /* 响应式调整 */
        @media (max-aspect-ratio: 4/3) {
            .test-info h1 { font-size: 3vw; }
            .test-info p { font-size: 1.5vw; }
            .zoom-display { font-size: 1.8vw; }
            .test-btn { font-size: 1.3vw; }
            .instructions { font-size: 1.2vw; }
        }

        @media (min-aspect-ratio: 21/9) {
            .test-info h1 { font-size: 1.5vw; }
            .test-info p { font-size: 0.8vw; }
            .zoom-display { font-size: 1vw; }
            .test-btn { font-size: 0.7vw; }
            .instructions { font-size: 0.6vw; }
        }
    </style>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="network_selector.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
</head>
<body>
    <!-- 测试覆盖层 (默认隐藏) -->
    <div class="test-overlay" id="testOverlay" style="display: none;">
        <div class="test-info">
            <h1>🎯 vw/vh 视口单位方案 - 最终验证</h1>
            <p>您的页面已成功转换为使用 vw/vh 视口单位的响应式方案</p>
            
            <div class="zoom-display" id="zoomDisplay">
                当前缩放: 100% | 视口: 1920×1080
            </div>

            <div class="instructions">
                <p><strong>测试说明：</strong></p>
                <ul>
                    <li>使用 Ctrl + 滚轮 或 Ctrl + +/- 缩放页面</li>
                    <li>观察所有元素是否保持完美的比例关系</li>
                    <li>在 50%-200% 缩放范围内测试</li>
                    <li>调整浏览器窗口大小验证响应性</li>
                    <li>按 Ctrl+Shift+T 可随时切换此信息</li>
                </ul>
            </div>

            <div class="test-buttons">
                <button class="test-btn" onclick="hideTestOverlay()">关闭此窗口</button>
                <button class="test-btn" onclick="showComparisonTest()">查看对比</button>
                <button class="test-btn" onclick="showGuide()">查看文档</button>
            </div>
        </div>
    </div>

    <!-- 主页面内容 -->
    <div class="main-page" id="mainPage" style="opacity: 1; pointer-events: auto;">
        <div class="container">
            <!-- 修改为水平布局 -->
            <main>
                <div class="left-panel">
                    <!-- 配置部分的内容 -->
                    <section class="card" id="networkSection">
                        <h2>大型活动场景</h2>
                        <div class="option-group">
                            <div class="option">
                                <input type="radio" id="net-preset" name="network" value="preset" checked>
                                <label for="net-preset">雄安体育场大型体育赛事</label>
                            </div>
                            <div class="option">
                                <input type="radio" id="net-custom" name="network" value="custom">
                                <label for="net-custom">自定义场景</label>
                            </div>
                            <button class="upload-btn" id="uploadNetBtn">
                                <i class="bi bi-upload"></i> 加载文件(*.net.xml)
                            </button>
                        </div>
                    </section>

                    <section class="card" id="trafficSection">
                        <h2>交通需求</h2>
                        <div class="option-group">
                            <div class="option">
                                <input type="radio" id="traffic-preset" name="traffic" value="preset" checked>
                                <label for="traffic-preset">预设</label>
                            </div>
                            <div class="option">
                                <input type="radio" id="traffic-custom" name="traffic" value="custom">
                                <label for="traffic-custom">自定义</label>
                            </div>
                            <button class="upload-btn" id="uploadRouBtn">
                                <i class="bi bi-upload"></i> 加载文件(*.rou.xml)
                            </button>
                        </div>
                        <div class="sub-options">
                            <div class="vehicle-time-container">
                                <div class="sub-option">
                                    <span class="bullet">•</span>
                                    <label>车辆类型：</label>
                                    <div class="select-wrapper">
                                        <select id="vehicleType">
                                            <option value="general">一般车辆</option>
                                            <option value="vip">贵宾专车</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="vehicle-time-container">
                                <div class="sub-option">
                                    <span class="bullet">•</span>
                                    <label>交通规模：</label>
                                    <div class="select-wrapper">
                                        <select id="trafficScale">
                                            <option value="small">小</option>
                                            <option value="medium" selected>中</option>
                                            <option value="large">大</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="sub-option">
                                    <span class="bullet">•</span>
                                    <label>组织时段：</label>
                                    <div class="select-wrapper">
                                        <select id="timePhase">
                                            <option value="entrance">进场</option>
                                            <option value="exit">离场</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section class="card" id="organizationSection">
                        <h2>组织方案</h2>
                        <div class="sub-options">
                            <div class="org-options-layout">
                                <!-- 第一列：道路相关选项 -->
                                <div class="org-column">
                                    <div class="org-group">
                                        <div class="sub-option">
                                            <span class="bullet">•</span>
                                            <label>出入口方案：</label>
                                            <div class="select-wrapper">
                                                <button class="select-btn" id="selectEntranceBtn">
                                                    <i class="bi bi-geo-alt"></i> 选择开放的出入口
                                                </button>
                                                <span id="entranceSelectionStatus" class="selection-status"></span>
                                            </div>
                                        </div>
                                        <div class="sub-option">
                                            <span class="bullet">•</span>
                                            <label>道路限行：</label>
                                            <div class="checkbox-wrapper">
                                                <input type="checkbox" id="roadRestriction">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 第二列：车辆和信号相关选项 -->
                                <div class="org-column">
                                    <div class="org-group">
                                        <div class="sub-option">
                                            <span class="bullet">•</span>
                                            <label>贵宾专车优先通行：</label>
                                            <div class="checkbox-wrapper">
                                                <input type="checkbox" id="vipPriority">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="org-group signal-group">
                                        <div class="sub-option">
                                            <span class="bullet">•</span>
                                            <label>信号配时：</label>
                                            <div class="signal-options">
                                                <div class="option-group compact">
                                                    <div class="option">
                                                        <input type="radio" id="signal-preset" name="signal" value="preset" checked>
                                                        <label for="signal-preset">预设</label>
                                                    </div>
                                                    <div class="option">
                                                        <input type="radio" id="signal-custom" name="signal" value="custom">
                                                        <label for="signal-custom">自定义</label>
                                                    </div>
                                                </div>
                                                <button class="upload-btn" id="uploadAddBtn">
                                                    <i class="bi bi-upload"></i> 加载文件(*.add.xml)
                                                </button>

                                            </div>
                                        </div>
                                        <div class="sub-option">
                                            <span class="bullet">•</span>
                                            <label>信控优化：</label>
                                            <div class="checkbox-wrapper">
                                                <input type="checkbox" id="signalOptimization">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section class="card" id="analysisSection">
                        <h2>分析配置</h2>
                        <div class="sub-options">
                            <div class="sub-option">
                                <span class="bullet">•</span>
                                <label>用户自选路段指标分析：</label>
                                <div class="checkbox-wrapper">
                                    <input type="checkbox" id="edgeAnalysis">
                                </div>
                                <span id="edgeAnalysisStatus" class="analysis-status"></span>
                            </div>
                            <div id="edgeAnalysisDetails" class="edge-analysis-details analysis-details">
                                <div class="sub-option">
                                    <button class="upload-btn" id="selectEdgesBtn">
                                        <i class="bi bi-geo-alt"></i> 选择分析路段
                                    </button>
                                </div>
                            </div>
                        </div>
                    </section>

                    <div class="action-buttons">
                        <button class="run-btn" onclick="window.startSimulation()">运行方案</button>
                        <button class="load-btn" id="loadResultBtn">
                            <i class="bi bi-file-earmark-text"></i> 加载已有结果
                        </button>
                        <button class="history-btn" id="historyBtn">
                            <i class="bi bi-clock-history"></i> 历史方案
                        </button>
                    </div>
                </div>

                <!-- 右侧主内容区 -->
                <div class="right-panel">
                    <!-- 左侧区域：地图和仿真概况 -->
                    <div class="map-section">
                        <!-- 固定的选择器/地图容器 -->
                        <div id="fixed-selector-container" class="fixed-selector-container map-container">
                                                    <div id="default-map-view" class="default-map-view">
                            <div class="map-placeholder">
                                <i class="bi bi-map"></i>
                                <p>路网地图</p>
                                <p style="font-size: 0.6vw; margin-top: 0.5vh; color: #ffc619;">
                                    ✅ 使用 vw/vh 单位 - 完美缩放一致性
                                </p>
                            </div>
                        </div>
                        <!-- 选择器将在需要时显示在这里 -->
                        <div id="embedded-selector-view" class="embedded-selector-view"></div>
                        </div>
                        
                        <!-- 仿真概况与配置 -->
                        <section class="result-card simulation-info-config" id="simulationInfoConfig">
                            <div class="result-header">
                                <h2 class="simulation-title">仿真概况与配置</h2>
                                <div class="result-actions">
                                    <button id="compareButton" class="compare-action-btn">
                                        <i class="bi bi-bar-chart"></i> 方案对比
                                    </button>
                                </div>
                            </div>
                            <div class="simulation-info">
                                <div>
                                    <span><strong>仿真ID:</strong> <span id="simId">250702174456</span></span>
                                    <span class="sim-time"><strong>开始时间:</strong> <span id="startTime">2025-07-02 17:44:56</span></span>
                                </div>
                            </div>
                            <!-- 新增的对比方案信息显示栏 -->
                            <div id="comparisonSchemesInfo" class="comparison-schemes-info">
                                当前对比方案：无
                            </div>
                            <div id="comparisonSchemesSummaryList" class="comparison-schemes-summary">
                                <!-- 这里由 JS 填充每个方案的配置摘要 -->
                            </div>
                            <div class="config-summary">
                                <div class="config-comparison">
                                    <div class="current-config">
                                        <h3>当前方案</h3>
                                        <div class="config-item" id="networkConfig">
                                            <span><strong>路网配置:</strong> <span>预设路网 - 仅开放东侧出入口 + 道路限行(2个路段)</span></span>
                                        </div>
                                        <div class="config-item" id="signalConfig">
                                            <span><strong>信号配置:</strong> <span>预设配时 + 自定义优化(2个交叉口)</span></span>
                                        </div>
                                        <div class="config-item" id="trafficConfig">
                                            <span><strong>交通需求:</strong> <span>预设需求 - 进场场景 + 中规模 + 贵宾专车</span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>
                    
                    <!-- 右侧区域：指标图表 -->
                    <div class="charts-section">
                        <div class="charts-header">
                            <h3>交通指标分析</h3>
                        </div>
                        <div class="charts-container">
                                <section class="result-card">
                                    <div class="result-header">
                                        <h2 class="chart-title">平均行程时间对比</h2>
                                    </div>
                                    <div class="chart-container">
                                        <canvas id="travelTimeChart"></canvas>
                                    </div>
                                </section>

                                <section class="result-card">
                                    <div class="result-header">
                                        <h2 class="chart-title">平均等待时间对比</h2>
                                    </div>
                                    <div class="chart-container">
                                        <canvas id="waitingTimeChart"></canvas>
                                    </div>
                                </section>

                                <section class="result-card">
                                    <div class="result-header">
                                        <h2 class="chart-title">平均等待次数对比</h2>
                                    </div>
                                    <div class="chart-container">
                                        <canvas id="waitingCountChart"></canvas>
                                    </div>
                                </section>

                                <section class="result-card">
                                    <div class="result-header">
                                        <h2 class="chart-title">时间损失与延误对比</h2>
                                    </div>
                                    <div class="chart-container">
                                        <canvas id="timeLossDelayChart"></canvas>
                                    </div>
                                </section>

                                <!-- 添加路段分析结果显示区域 -->
                                <section class="result-card edge-analysis-section" id="edgeAnalysisSection">
                                    <div class="result-header">
                                        <h2 class="chart-title">用户自选路段分析结果</h2>
                                    </div>
                                    <div class="edge-analysis-container">
                                        <div class="edge-analysis-column">
                                            <h3 class="metrics-subtitle">行人路段指标</h3>
                                            <div class="metrics-grid edge-metrics-grid">
                                                <div class="metric-item edge-metric-item">
                                                    <div class="metric-value" id="edgePedTravelTime">-</div>
                                                    <div class="metric-label">平均行程时间 (秒)</div>
                                                </div>
                                                <div class="metric-item edge-metric-item">
                                                    <div class="metric-value" id="edgePedWaitingTime">-</div>
                                                    <div class="metric-label">平均等待时间 (秒)</div>
                                                </div>
                                                <div class="metric-item edge-metric-item">
                                                    <div class="metric-value" id="edgePedTimeLoss">-</div>
                                                    <div class="metric-label">平均时间损失 (秒)</div>
                                                </div>
                                                <div class="metric-item edge-metric-item">
                                                    <div class="metric-value" id="edgePedSpeed">-</div>
                                                    <div class="metric-label">平均速度 (m/s)</div>
                                                </div>
                                                <div class="metric-item edge-metric-item">
                                                    <div class="metric-value" id="edgePedCount">-</div>
                                                    <div class="metric-label">总进入数 (人次)</div>
                                                </div>
                                                <div class="metric-item edge-metric-item">
                                                    <div class="metric-value" id="edgePedOccupancy">-</div>
                                                    <div class="metric-label">平均占用率 (%)</div>
                                                </div>
                                            </div>
                                            <div class="chart-container edge-chart-container">
                                                <canvas id="edgePedChart"></canvas>
                                            </div>
                                        </div>
                                        <div class="edge-analysis-column">
                                            <h3 class="metrics-subtitle">车辆路段指标</h3>
                                            <div class="metrics-grid edge-metrics-grid">
                                                <div class="metric-item edge-metric-item">
                                                    <div class="metric-value" id="edgeVehTravelTime">-</div>
                                                    <div class="metric-label">平均行程时间 (秒)</div>
                                                </div>
                                                <div class="metric-item edge-metric-item">
                                                    <div class="metric-value" id="edgeVehWaitingTime">-</div>
                                                    <div class="metric-label">平均等待时间 (秒)</div>
                                                </div>
                                                <div class="metric-item edge-metric-item">
                                                    <div class="metric-value" id="edgeVehTimeLoss">-</div>
                                                    <div class="metric-label">平均时间损失 (秒)</div>
                                                </div>
                                                <div class="metric-item edge-metric-item">
                                                    <div class="metric-value" id="edgeVehSpeed">-</div>
                                                    <div class="metric-label">平均速度 (m/s)</div>
                                                </div>
                                                <div class="metric-item edge-metric-item">
                                                    <div class="metric-value" id="edgeVehCount">-</div>
                                                    <div class="metric-label">总进入数 (车次)</div>
                                                </div>
                                                <div class="metric-item edge-metric-item">
                                                    <div class="metric-value" id="edgeVehOccupancy">-</div>
                                                    <div class="metric-label">平均占用率 (%)</div>
                                                </div>
                                            </div>
                                            <div class="chart-container edge-chart-container">
                                                <canvas id="edgeVehChart"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </section>
                            </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 历史方案模态框 -->
    <div id="historyModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>历史方案管理</h3>
                <span class="close" id="closeHistoryModal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="history-controls">
                    <button id="saveCurrentBtn" class="save-btn">
                        <i class="bi bi-floppy"></i> 保存当前配置
                    </button>
                    <button id="refreshHistoryBtn" class="refresh-btn">
                        <i class="bi bi-arrow-clockwise"></i> 刷新列表
                    </button>
                </div>

                <div class="history-list" id="historyList">
                    <div class="loading-message">正在加载历史方案...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 保存方案模态框 -->
    <div id="saveSchemeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>保存配置方案</h3>
                <span class="close" id="closeSaveModal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="schemeName">方案名称：</label>
                    <input type="text" id="schemeName" placeholder="请输入方案名称（可选）">
                </div>
                <div class="form-group">
                    <label for="schemeDescription">方案描述：</label>
                    <textarea id="schemeDescription" placeholder="请输入方案描述（可选）" rows="3"></textarea>
                </div>
                <div class="config-preview">
                    <h4>配置预览：</h4>
                    <div id="configPreview" class="config-summary"></div>
                </div>
                <div class="modal-actions">
                    <button id="confirmSaveBtn" class="confirm-btn">确认保存</button>
                    <button id="cancelSaveBtn" class="cancel-btn">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script src="debug.js"></script>
    <script src="script.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="network_selector.js"></script>
    <script>
        // 确保startSimulation函数可以被外部访问
        window.startSimulation = window.startSimulation || function() {
            alert("系统正在加载中，请稍后再试...");
            console.log("startSimulation函数还未加载，尝试从script.js中查找...");
            // 触发运行按钮的点击，以便触发script.js中的事件监听器
            setTimeout(() => {
                // 使用原生DOM事件触发
                const runBtn = document.querySelector('.run-btn');
                if (runBtn) {
                    const event = new MouseEvent('click', {
                        'view': window,
                        'bubbles': true,
                        'cancelable': true
                    });
                    runBtn.dispatchEvent(event);
                }
            }, 500);
        };
        
        document.addEventListener('DOMContentLoaded', function() {
            // 构建配置JSON - 供script.js中的startSimulation函数使用
            window.buildConfigJSON = function() {
                // 获取路网配置
                const networkType = document.querySelector('input[name="network"]:checked').value;
                const entranceType = getEntrancePlanFromSelection();
                const roadRestriction = document.getElementById('roadRestriction').checked;

                // 获取信号配置
                const signalType = document.querySelector('input[name="signal"]:checked').value;
                const signalOptimization = document.getElementById('signalOptimization').checked;

                // 获取交通需求配置
                const trafficType = document.querySelector('input[name="traffic"]:checked').value;
                const vehicleType = document.getElementById('vehicleType').value;
                const trafficScale = document.getElementById('trafficScale').value;
                const timePhase = document.getElementById('timePhase').value;
                const vipPriority = document.getElementById('vipPriority').checked;


                // 获取分析配置
                const edgeAnalysis = document.getElementById('edgeAnalysis').checked;

                // 构建完整配置对象
                return {
                    "network_config": {
                        "type": networkType === "preset" ? "predefined" : "custom",
                        "file_path": networkType === "custom" ? "custom_network.net.xml" : null,
                        "entrance_plan": entranceType,
                        "road_restriction": {
                            "enabled": roadRestriction,
                            "restricted_edges": roadRestriction ? getRestrictedEdges() : []
                        }
                    },
                    "signal_config": {
                        "type": signalType === "preset" ? "predefined" : "custom",
                        "file_path": signalType === "custom" ? "custom_signal.add.xml" : null,
                        "optimization": {
                            "enabled": signalOptimization,
                            "selected_intersections": signalOptimization ? getSelectedIntersections() : []
                        }
                    },
                    "traffic_config": {
                        "type": trafficType === "preset" ? "predefined" : "custom",
                        "file_path": trafficType === "custom" ? "custom_traffic.rou.xml" : null,
                        "scenario": timePhaseToText(timePhase),
                        "vehicle_type": vehicleTypeToText(vehicleType),
                        "traffic_scale": trafficScale,
                        "vip_priority": {
                            "enabled": vipPriority
                        }
                    },
                    "analysis_config": {
                        "edge_analysis": {
                            "enabled": edgeAnalysis,
                            "selected_edges": edgeAnalysis ? getSelectedAnalysisEdges() : []
                        }
                    }
                };
            }

            // 获取出入口方案文本描述
            function getEntrancePlanFromSelection() {
                const selectedEdges = window.selectedEntranceEdges || [];

                if (selectedEdges.length === 0) {
                    return '仅开放东侧出入口'; // 默认值
                }

                // 检查是否选择了东侧出入口路段
                const hasEastEntrance = selectedEdges.some(edge =>
                    edge === 'people_east_in' || edge === 'people_east_out'
                );

                // 检查是否选择了南侧出入口路段
                const hasSouthEntrance = selectedEdges.some(edge =>
                    edge === 'people_south_in' || edge === 'people_south_out'
                );

                // 根据选择情况决定出入口方案
                if (hasEastEntrance && hasSouthEntrance) {
                    return '全部开放';
                } else if (hasSouthEntrance && !hasEastEntrance) {
                    return '仅开放南侧出入口';
                } else {
                    return '仅开放东侧出入口';
                }
            }

            // 辅助函数，转换车辆类型选择为文本
            function vehicleTypeToText(type) {
                switch(type) {
                    case "general": return "仅一般车辆";
                    case "vip": return "存在贵宾专车";
                    default: return "仅一般车辆";
                }
            }

            // 辅助函数，转换时段选择为文本
            function timePhaseToText(phase) {
                switch(phase) {
                    case "entrance": return "进场";
                    case "exit": return "离场";
                    default: return "进场";
                }
            }

            // 获取已选择的交叉口列表（从全局状态获取）
            function getSelectedIntersections() {
                // 从script.js中的全局变量获取
                // 注意：如果选择器返回了混合结果（交叉口+路段），只返回交叉口部分
                const intersections = window.selectedIntersections || [];
                const optimizationEdges = window.selectedOptimizationEdges || [];

                // 如果存在优化路段，说明是混合选择，需要合并返回
                if (optimizationEdges.length > 0) {
                    return {
                        intersections: intersections,
                        edges: optimizationEdges
                    };
                }

                // 否则只返回交叉口（保持向后兼容）
                return intersections;
            }

            // 获取已选择的限行路段列表（从全局状态获取）
            function getRestrictedEdges() {
                // 从script.js中的全局变量获取
                return window.selectedRestrictedEdges || [];
            }
            // 获取已选择的分析路段列表（从全局状态获取）
            function getSelectedAnalysisEdges() {
                // 从script.js中的全局变量获取
                return window.selectedAnalysisEdges || [];
            }

            // 构建结果数据，适配新的JSON格式
            window.navigateToResultPage = function(data) {
                // 构建结果数据，适配新的JSON格式
                const resultData = {
                    "simulation_id": data.simulation_id,
                    "config_summary": data.config_summary,
                    "start_time": new Date().toISOString(),  // 使用当前时间
                    "simulation_results": data.metrics  // 直接使用后端返回的新格式metrics
                };

                // 将数据编码为URL参数
                const encodedData = encodeURIComponent(JSON.stringify(resultData));

                // 跳转到结果页面
                window.location.href = `results.html?data=${encodedData}`;
            }

            // 显示错误信息 - 供script.js使用
            window.showError = function(message) {
                alert(`错误: ${message}`);
            }

            // ==================== 路网地图加载功能 ====================

            // 主初始化函数 - 调用 script.js 中已有的地图功能
            function initializeNetworkMapLoader() {
                console.log('开始初始化路网地图加载器...');

                // 检查必要的依赖
                if (typeof L === 'undefined') {
                    console.error('Leaflet 库未加载，延迟重试...');
                    setTimeout(initializeNetworkMapLoader, 1000);
                    return;
                }

                // 检查 script.js 中的地图初始化函数是否可用
                if (typeof window.initializeMap === 'function') {
                    console.log('使用 script.js 中的地图初始化函数');
                    setTimeout(() => {
                        try {
                            window.initializeMap();
                        } catch (error) {
                            console.error('调用 script.js 地图初始化函数失败:', error);
                        }
                    }, 2000);
                } else {
                    console.log('script.js 中的地图初始化函数不可用，使用简化版本');
                    setTimeout(initializeSimpleMap, 2000);
                }
            }

            // 简化的地图初始化函数（备用方案）
            function initializeSimpleMap() {
                console.log('初始化简化版地图...');

                // 获取地图容器
                const mapContainer = document.querySelector('.map-placeholder');
                if (!mapContainer) {
                    console.error('找不到地图容器 .map-placeholder');
                    return;
                }

                // 更新占位符显示
                mapContainer.innerHTML = '<div class="map-loading"><i class="bi bi-map"></i><p>正在加载路网地图...</p></div>';

                // 尝试调用 script.js 中的其他地图相关函数
                setTimeout(() => {
                    if (typeof window.initializeDefaultMap === 'function') {
                        console.log('调用 initializeDefaultMap 函数');
                        window.initializeDefaultMap();
                    } else {
                        console.log('未找到可用的地图初始化函数');
                        mapContainer.innerHTML = '<div class="map-placeholder"><i class="bi bi-map"></i><p>路网地图</p><small>请点击"运行方案"查看仿真地图</small></div>';
                    }
                }, 1000);
            }

            // 添加窗口大小变化时重新绘制图表的监听器
            window.addEventListener('resize', function() {
                // 如果已经存在图表实例，则在窗口调整大小后重新渲染
                if (window.pedestrianChart instanceof Chart) {
                    window.pedestrianChart.resize();
                }
                if (window.vehicleChart instanceof Chart) {
                    window.vehicleChart.resize();
                }
                if (window.vipVehicleChart instanceof Chart) {
                    window.vipVehicleChart.resize();
                }
                if (window.venueChart instanceof Chart) {
                    window.venueChart.resize();
                }
                if (window.edgePedChart instanceof Chart) {
                    window.edgePedChart.resize();
                }
                if (window.edgeVehChart instanceof Chart) {
                    window.edgeVehChart.resize();
                }
            });

            // 初始化路网地图加载功能
            initializeNetworkMapLoader();

            // 只有当页面包含结果数据参数时才加载和显示结果
            if (window.location.search.includes('data=')) {
                loadResultData().then(data => {
                    updateDisplay(data);
                    // 保存已加载的结果数据
                    currentLoadedResultData = JSON.parse(JSON.stringify(data));
                });
            } else {
                // 如果没有携带 data 参数，加载默认结果数据并更新展示，
                // 确保 currentDisplayedData 与 currentLoadedResultData 初始化完成，
                // 以防后续方案对比中出现"随机"数值。
                loadResultData().then(data => {
                    updateDisplay(data);
                    // 保存已加载的结果数据
                    currentLoadedResultData = JSON.parse(JSON.stringify(data));
                });
            }

            // 添加方案对比功能
            document.getElementById('historyBtn').addEventListener('click', function() {
                document.getElementById('historyModal').style.display = 'block';
                getDefaultResultData();
                document.getElementById('refreshHistoryBtn').click();
            });

            // 直接方案对比按钮：弹窗并刷新
            document.getElementById('compareButton').addEventListener('click', function() {
                document.getElementById('historyModal').style.display = 'block';
                getDefaultResultData();
                document.getElementById('refreshHistoryBtn').click();

            });

            // 关闭历史方案对话框
            document.getElementById('closeHistoryModal').addEventListener('click', function() {
                document.getElementById('historyModal').style.display = 'none';
            });

            // 加载历史方案列表
            function loadHistorySchemes() {
                const historyList = document.getElementById('historyList');
                historyList.innerHTML = '<div class="loading-message">正在加载历史方案...</div>';

                // 模拟获取历史方案数据
                setTimeout(function() {
                    const sampleSchemes = [
                        {id: "250702174456", name: "方案A - 东侧出入口", date: "2025-07-02 17:44:56"},
                        {id: "250702185532", name: "方案B - 南侧出入口", date: "2025-07-02 18:55:32"},
                        {id: "250703093022", name: "方案C - 全部开放", date: "2025-07-03 09:30:22"}
                    ];

                    if (sampleSchemes.length === 0) {
                        historyList.innerHTML = '<div class="empty-message">暂无历史方案</div>';
                        return;
                    }

                    let html = '';
                    sampleSchemes.forEach(scheme => {
                        html += `
                        <div class="history-item">
                            <div class="history-info">
                                <div class="history-name">${scheme.name}</div>
                                <div class="history-date">${scheme.date}</div>
                            </div>
                            <div class="history-actions">
                                <button class="view-btn" data-id="${scheme.id}">查看</button>
                                <button class="compare-btn" data-id="${scheme.id}">对比</button>
                                <button class="delete-btn" data-id="${scheme.id}">删除</button>
                            </div>
                        </div>`;
                    });

                    historyList.innerHTML = html;

                    // 添加按钮事件监听
                    document.querySelectorAll('.view-btn').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const schemeId = this.getAttribute('data-id');
                            viewScheme(schemeId);
                        });
                    });

                    document.querySelectorAll('.compare-btn').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const schemeId = this.getAttribute('data-id');
                            compareScheme(schemeId);
                        });
                    });

                    document.querySelectorAll('.delete-btn').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const schemeId = this.getAttribute('data-id');
                            deleteScheme(schemeId);
                        });
                    });
                }, 1000);
            }

            // 查看方案详情
            function viewScheme(schemeId) {
                console.log("查看方案:", schemeId);
                // 模拟加载方案数据并显示
                loadSchemeData(schemeId).then(data => {
                    updateDisplay(data);
                    document.getElementById('historyModal').style.display = 'none';
                });
            }

            // 添加一个全局变量存储当前已加载的结果数据
            var currentLoadedResultData = null;
            // 添加一个变量存储已加载的历史方案数据
            var loadedSchemeData = {};
            // 添加一个变量用于存储当前页面上显示的指标数据
            var currentDisplayedData = null;

            // 对比方案
            function compareScheme(schemeId) {
                console.log("开始对比方案:", schemeId);
                console.log("对比开始时数据状态 - 当前显示数据:",
                            currentDisplayedData ? currentDisplayedData.simulation_id : "不存在",
                            "当前加载数据:",
                            currentLoadedResultData ? currentLoadedResultData.simulation_id : "不存在");

                // 使用已缓存的当前数据或加载数据
                const getCurrentData = async () => {
                    // 如果已经有显示的数据，直接使用
                    if (currentDisplayedData) {
                        console.log("使用当前显示的数据进行对比:", currentDisplayedData.simulation_id);
                        return JSON.parse(JSON.stringify(currentDisplayedData)); // 返回完全独立的深拷贝
                    }
                    // 如果已经有加载的数据，直接使用
                    else if (currentLoadedResultData) {
                        console.log("使用已加载的真实数据进行对比:", currentLoadedResultData.simulation_id);
                        return JSON.parse(JSON.stringify(currentLoadedResultData)); // 返回完全独立的深拷贝
                    } else {
                        // 否则尝试加载数据
                        console.log("没有缓存数据，尝试重新加载");
                        return await loadResultData();
                    }
                };

                // 加载当前方案和选定方案的数据
                Promise.all([
                    getCurrentData(),
                    loadSchemeData(schemeId)
                ]).then(([currentData, compareData]) => {
                    console.log("已获取当前方案数据:", currentData.simulation_id);
                    console.log("已获取对比方案数据:", compareData.simulation_id);

                    // 确保使用完全独立的数据对象
                    showComparison(
                        JSON.parse(JSON.stringify(currentData)),
                        JSON.parse(JSON.stringify(compareData))
                    );
                    document.getElementById('historyModal').style.display = 'none';
                });
            }

            // 删除方案
            function deleteScheme(schemeId) {
                if (confirm(`确定要删除方案 ${schemeId} 吗？`)) {
                    console.log("删除方案:", schemeId);
                    // 模拟删除操作
                    alert("方案已删除");
                    loadHistorySchemes(); // 重新加载列表
                }
            }
        });

        function updateZoomDisplay() {
            const zoomDisplay = document.getElementById('zoomDisplay');
            if (zoomDisplay) {
                const zoom = Math.round((window.outerWidth / window.innerWidth) * 100);
                const vw = window.innerWidth;
                const vh = window.innerHeight;
                zoomDisplay.textContent = `当前缩放: ${zoom}% | 视口: ${vw}×${vh}`;
            }
        }

        function startTest() {
            document.getElementById('testOverlay').style.display = 'none';
            document.getElementById('mainPage').style.opacity = '1';
            document.getElementById('mainPage').style.pointerEvents = 'auto';
        }

        function showComparison(currentData, compareData) {
            // 添加对比标题
            addComparisonHeader(compareData.simulation_id);

            // 创建对比图表
            createComparisonCharts(currentData, compareData);

            // 显示对比方案的配置
            displayComparisonConfig(compareData);
        }
        
        // 显示对比测试页面的函数
        function showComparisonTest() {
            window.open('comparison-test.html', '_blank');
        }

        function showGuide() {
            window.open('viewport-units-guide.md', '_blank');
        }

        // 初始化
        updateZoomDisplay();

        // 监听窗口变化
        window.addEventListener('resize', updateZoomDisplay);

        console.log('🎯 vw/vh 视口单位方案已成功应用到 final-test.html！');
        console.log('📋 测试重点：');
        console.log('1. 使用 Ctrl + 滚轮缩放页面');
        console.log('2. 观察所有元素是否保持完美比例');
        console.log('3. 在不同缩放比例下测试交互功能');
        console.log('✅ 预期效果：完美的缩放一致性 + 完整业务功能');
        
        function showTestOverlay() {
            const overlay = document.getElementById('testOverlay');
            if (overlay) {
                overlay.style.display = 'flex';
                updateZoomDisplay();
            }
        }

        function hideTestOverlay() {
            const overlay = document.getElementById('testOverlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }

        // 添加键盘快捷键来显示/隐藏测试覆盖层
        document.addEventListener('keydown', function(e) {
            // Ctrl + Shift + T 显示测试覆盖层
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                const overlay = document.getElementById('testOverlay');
                if (overlay.style.display === 'none' || overlay.style.display === '') {
                    showTestOverlay();
                } else {
                    hideTestOverlay();
                }
            }
        });

        // 创建平均行程时间柱状图（合并所有类型）
        function createTravelTimeChart(metrics) {
            const ctx = document.getElementById('travelTimeChart').getContext('2d');
            
            // 销毁已存在的图表实例
            if (window.travelTimeChart instanceof Chart) {
                window.travelTimeChart.destroy();
            }
            
            window.travelTimeChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['行人', '一般车辆', '贵宾专车', '场馆区域行人', '场馆区域车辆'],
                    datasets: [{
                        label: '平均行程时间 (秒)',
                        data: [
                            metrics.pedestrian_metrics.average_travel_time,
                            metrics.vehicle_metrics.average_travel_time,
                            metrics.vip_vehicle_metrics.average_travel_time,
                            metrics.venue_area_metrics.average_pedestrian_travel_time,
                            metrics.venue_area_metrics.average_vehicle_travel_time
                        ],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 206, 86, 0.8)',
                            'rgba(75, 192, 192, 0.8)',
                            'rgba(153, 102, 255, 0.8)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        },
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            labels: { color: '#ffffff' }
                        }
                    }
                }
            });
        }

        // 创建平均等待时间柱状图（合并行人、车辆、贵宾车）
        function createWaitingTimeChart(metrics) {
            const ctx = document.getElementById('waitingTimeChart').getContext('2d');
            
            // 销毁已存在的图表实例
            if (window.waitingTimeChart instanceof Chart) {
                window.waitingTimeChart.destroy();
            }
            
            window.waitingTimeChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['行人', '一般车辆', '贵宾专车'],
                    datasets: [{
                        label: '平均等待时间 (秒)',
                        data: [
                            metrics.pedestrian_metrics.average_waiting_time,
                            metrics.vehicle_metrics.average_waiting_time,
                            metrics.vip_vehicle_metrics.average_waiting_time
                        ],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 206, 86, 0.8)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        },
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            labels: { color: '#ffffff' }
                        }
                    }
                }
            });
        }

        // 创建平均等待次数柱状图
        function createWaitingCountChart(metrics) {
            const ctx = document.getElementById('waitingCountChart').getContext('2d');
            
            // 销毁已存在的图表实例
            if (window.waitingCountChart instanceof Chart) {
                window.waitingCountChart.destroy();
            }
            
            window.waitingCountChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['行人', '一般车辆', '贵宾专车'],
                    datasets: [{
                        label: '平均等待次数',
                        data: [
                            metrics.pedestrian_metrics.average_waiting_count,
                            metrics.vehicle_metrics.average_waiting_count,
                            metrics.vip_vehicle_metrics.average_waiting_count
                        ],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 206, 86, 0.8)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        },
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            labels: { color: '#ffffff' }
                        }
                    }
                }
            });
        }

        // 创建时间损失和延误柱状图
        function createTimeLossDelayChart(metrics) {
            const ctx = document.getElementById('timeLossDelayChart').getContext('2d');
            
            // 销毁已存在的图表实例
            if (window.timeLossDelayChart instanceof Chart) {
                window.timeLossDelayChart.destroy();
            }
            
            window.timeLossDelayChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['行人时间损失', '一般车辆时间损失', '贵宾专车时间损失', '场馆区域行人延误', '场馆区域车辆延误'],
                    datasets: [{
                        label: '时间损失/延误 (秒)',
                        data: [
                            metrics.pedestrian_metrics.average_time_loss,
                            metrics.vehicle_metrics.average_time_loss,
                            metrics.vip_vehicle_metrics.average_time_loss,
                            metrics.venue_area_metrics.average_pedestrian_delay,
                            metrics.venue_area_metrics.average_vehicle_delay
                        ],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 206, 86, 0.8)',
                            'rgba(75, 192, 192, 0.8)',
                            'rgba(153, 102, 255, 0.8)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        },
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' },
                            maxRotation: 45,
                            minRotation: 45
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            labels: { color: '#ffffff' }
                        }
                    }
                }
            });
        }
        
        // 创建对比图表函数
        function createComparisonTravelTimeChart(currentMetrics, compareMetrics, compareId) {
            const ctx = document.getElementById('travelTimeChart').getContext('2d');
            
            // 销毁已存在的图表实例
            if (window.travelTimeChart instanceof Chart) {
                window.travelTimeChart.destroy();
            }
            
            window.travelTimeChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['行人', '一般车辆', '贵宾专车', '场馆区域行人', '场馆区域车辆'],
                    datasets: [
                        {
                            label: '当前方案',
                            data: [
                                currentMetrics.pedestrian_metrics.average_travel_time,
                                currentMetrics.vehicle_metrics.average_travel_time,
                                currentMetrics.vip_vehicle_metrics.average_travel_time,
                                currentMetrics.venue_area_metrics.average_pedestrian_travel_time,
                                currentMetrics.venue_area_metrics.average_vehicle_travel_time
                            ],
                            backgroundColor: 'rgba(54, 162, 235, 0.8)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        },
                        {
                            label: `对比方案 (${compareId})`,
                            data: [
                                compareMetrics.pedestrian_metrics.average_travel_time,
                                compareMetrics.vehicle_metrics.average_travel_time,
                                compareMetrics.vip_vehicle_metrics.average_travel_time,
                                compareMetrics.venue_area_metrics.average_pedestrian_travel_time,
                                compareMetrics.venue_area_metrics.average_vehicle_travel_time
                            ],
                            backgroundColor: 'rgba(255, 99, 132, 0.8)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        },
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            labels: { color: '#ffffff' }
                        }
                    }
                }
            });
        }
        
        // 添加其他对比图表创建函数
        function createComparisonWaitingTimeChart(currentMetrics, compareMetrics, compareId) {
            const ctx = document.getElementById('waitingTimeChart').getContext('2d');
            
            // 销毁已存在的图表实例
            if (window.waitingTimeChart instanceof Chart) {
                window.waitingTimeChart.destroy();
            }
            
            window.waitingTimeChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['行人', '一般车辆', '贵宾专车'],
                    datasets: [
                        {
                            label: '当前方案',
                            data: [
                                currentMetrics.pedestrian_metrics.average_waiting_time,
                                currentMetrics.vehicle_metrics.average_waiting_time,
                                currentMetrics.vip_vehicle_metrics.average_waiting_time
                            ],
                            backgroundColor: 'rgba(54, 162, 235, 0.8)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        },
                        {
                            label: `对比方案 (${compareId})`,
                            data: [
                                compareMetrics.pedestrian_metrics.average_waiting_time,
                                compareMetrics.vehicle_metrics.average_waiting_time,
                                compareMetrics.vip_vehicle_metrics.average_waiting_time
                            ],
                            backgroundColor: 'rgba(255, 99, 132, 0.8)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        },
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            labels: { color: '#ffffff' }
                        }
                    }
                }
            });
        }

        function createComparisonWaitingCountChart(currentMetrics, compareMetrics, compareId) {
            const ctx = document.getElementById('waitingCountChart').getContext('2d');
            
            if (window.waitingCountChart instanceof Chart) {
                window.waitingCountChart.destroy();
            }
            
            window.waitingCountChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['行人', '一般车辆', '贵宾专车'],
                    datasets: [
                        {
                            label: '当前方案',
                            data: [
                                currentMetrics.pedestrian_metrics.average_waiting_count,
                                currentMetrics.vehicle_metrics.average_waiting_count,
                                currentMetrics.vip_vehicle_metrics.average_waiting_count
                            ],
                            backgroundColor: 'rgba(54, 162, 235, 0.8)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        },
                        {
                            label: `对比方案 (${compareId})`,
                            data: [
                                compareMetrics.pedestrian_metrics.average_waiting_count,
                                compareMetrics.vehicle_metrics.average_waiting_count,
                                compareMetrics.vip_vehicle_metrics.average_waiting_count
                            ],
                            backgroundColor: 'rgba(255, 99, 132, 0.8)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        },
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            labels: { color: '#ffffff' }
                        }
                    }
                }
            });
        }
        
        function createComparisonTimeLossDelayChart(currentMetrics, compareMetrics, compareId) {
            const ctx = document.getElementById('timeLossDelayChart').getContext('2d');
            
            if (window.timeLossDelayChart instanceof Chart) {
                window.timeLossDelayChart.destroy();
            }
            
            window.timeLossDelayChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['行人时间损失', '一般车辆时间损失', '贵宾专车时间损失', '场馆区域行人延误', '场馆区域车辆延误'],
                    datasets: [
                        {
                            label: '当前方案',
                            data: [
                                currentMetrics.pedestrian_metrics.average_time_loss,
                                currentMetrics.vehicle_metrics.average_time_loss,
                                currentMetrics.vip_vehicle_metrics.average_time_loss,
                                currentMetrics.venue_area_metrics.average_pedestrian_delay,
                                currentMetrics.venue_area_metrics.average_vehicle_delay
                            ],
                            backgroundColor: 'rgba(54, 162, 235, 0.8)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        },
                        {
                            label: `对比方案 (${compareId})`,
                            data: [
                                compareMetrics.pedestrian_metrics.average_time_loss,
                                compareMetrics.vehicle_metrics.average_time_loss,
                                compareMetrics.vip_vehicle_metrics.average_time_loss,
                                compareMetrics.venue_area_metrics.average_pedestrian_delay,
                                compareMetrics.venue_area_metrics.average_vehicle_delay
                            ],
                            backgroundColor: 'rgba(255, 99, 132, 0.8)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        },
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' },
                            maxRotation: 45,
                            minRotation: 45
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            labels: { color: '#ffffff' }
                        }
                    }
                }
            });
        }
        
        // 确保所有指标都有默认值
        function ensureAllMetricsDefaults(allMetrics) {
            // 行人
            allMetrics.pedestrian_metrics = {
                average_travel_time: 0,
                average_waiting_time: 0,
                average_waiting_count: 0,
                average_time_loss: 0,
                ...allMetrics.pedestrian_metrics
            };
            // 一般车辆
            allMetrics.vehicle_metrics = {
                average_travel_time: 0,
                average_waiting_time: 0,
                average_waiting_count: 0,
                average_time_loss: 0,
                ...allMetrics.vehicle_metrics
            };
            // VIP车
            allMetrics.vip_vehicle_metrics = {
                average_travel_time: 0,
                average_waiting_time: 0,
                average_waiting_count: 0,
                average_time_loss: 0,
                ...allMetrics.vip_vehicle_metrics
            };
            // 场馆
            allMetrics.venue_area_metrics = {
                average_vehicle_travel_time: 0,
                average_vehicle_delay: 0,
                average_pedestrian_travel_time: 0,
                average_pedestrian_delay: 0,
                ...allMetrics.venue_area_metrics
            };
        }

        // 加载结果数据
        async function loadResultData() {
            try {
                // 尝试加载真实的JSON文件
                const response = await fetch('simulation_result_250801162457.json');
                if (response.ok) {
                    const data = await response.json();
                    console.log('成功加载真实JSON数据:', data.simulation_id);
                    return data;
                }
            } catch (error) {
                console.warn('加载真实JSON文件失败，使用默认数据:', error);
            }
            // 如果加载失败，返回默认数据
            return getDefaultResultData();
        }

        // 获取默认数据
        function getDefaultResultData() {
            return {
                simulation_id: "250702174456",
                start_time: new Date().toISOString(),
                config_summary: {
                    network: "预设路网 - 仅开放东侧出入口 + 道路限行(2个路段)",
                    signal: "预设配时 + 自定义优化(2个交叉口)",
                    traffic: "预设需求 - 进场场景 + 中规模 + 贵宾专车"
                },
                simulation_results: {
                    pedestrian_metrics: {
                        average_travel_time: 123.45,
                        average_waiting_time: 12.34,
                        average_waiting_count: 2.1,
                        average_time_loss: 23.45
                    },
                    vehicle_metrics: {
                        average_travel_time: 234.56,
                        average_waiting_time: 45.67,
                        average_waiting_count: 3.2,
                        average_time_loss: 67.89
                    },
                    vip_vehicle_metrics: {
                        average_travel_time: 145.23,
                        average_waiting_time: 8.91,
                        average_waiting_count: 1.2,
                        average_time_loss: 12.34
                    },
                    venue_area_metrics: {
                        average_pedestrian_travel_time: 156.78,
                        average_pedestrian_delay: 34.56,
                        average_vehicle_travel_time: 267.89,
                        average_vehicle_delay: 78.90
                    }
                }
            };
        }
        
        // 显示对比方案的配置信息
        function displayComparisonConfig(compareData) {
            // 更新方案对比信息显示
            const comparisonSchemesInfo = document.getElementById('comparisonSchemesInfo');
            if (comparisonSchemesInfo) {
                // 显示方案ID和关键配置信息
                const networkSummary = compareData.config_summary?.network || '未知';
                const signalSummary = compareData.config_summary?.signal || '未知';
                comparisonSchemesInfo.innerHTML = `
                    <div><strong>当前对比方案：${compareData.simulation_id}</strong></div>
                    <div style="font-size: 0.9em; margin-top: 5px; color: #aaffaa;">路网: ${networkSummary}</div>
                    <div style="font-size: 0.9em; color: #ffbb88;">信号: ${signalSummary}</div>
                `;
                // 保持样式设置
                comparisonSchemesInfo.style.color = '#30abe8';
                comparisonSchemesInfo.style.fontWeight = 'bold';
            }

            // 生成详细的配置信息
            const detailedConfig = generateDetailedConfigDisplay(compareData.config);

            // 更新对比方案的配置信息摘要列表
            const summaryList = document.getElementById('comparisonSchemesSummaryList');
            if (summaryList) {
                summaryList.innerHTML = `
                    <div class="scheme-summary">
                        <div class="scheme-name">方案 ${compareData.simulation_id}</div>
                        <div class="scheme-details">
                            <div><strong>网络配置:</strong> ${compareData.config_summary.network}</div>
                            <div><strong>信号配置:</strong> ${compareData.config_summary.signal}</div>
                            <div><strong>交通配置:</strong> ${compareData.config_summary.traffic}</div>
                            ${detailedConfig}
                        </div>
                    </div>
                `;
            }
        }

        // 生成详细的配置显示信息
        function generateDetailedConfigDisplay(config) {
            let detailsHtml = '<div class="config-details" style="margin-top: 10px; font-size: 0.9em; color: #666;">';

            try {
                // 显示道路限行详情
                const roadRestriction = config?.network_config?.road_restriction;
                if (roadRestriction?.enabled) {
                    detailsHtml += '<div><strong>道路限行详情:</strong></div>';

                    // 车辆限行路段
                    const vehicleEdges = roadRestriction.vehicle_restricted_edges_with_names;
                    if (vehicleEdges && vehicleEdges.length > 0) {
                        detailsHtml += '<div style="margin-left: 15px;">• 车辆限行: ';
                        const vehicleNames = vehicleEdges.map(edge => edge.name || edge.id).join('、');
                        detailsHtml += vehicleNames + '</div>';
                    }

                    // 行人限行路段
                    const pedestrianEdges = roadRestriction.pedestrian_restricted_edges_with_names;
                    if (pedestrianEdges && pedestrianEdges.length > 0) {
                        detailsHtml += '<div style="margin-left: 15px;">• 行人限行: ';
                        const pedestrianNames = pedestrianEdges.map(edge => edge.name || edge.id).join('、');
                        detailsHtml += pedestrianNames + '</div>';
                    }
                }

                // 显示信号优化详情
                const optimization = config?.signal_config?.optimization;
                if (optimization?.enabled) {
                    const intersections = optimization.selected_intersections_with_names;
                    if (intersections && intersections.length > 0) {
                        detailsHtml += '<div><strong>优化交叉口详情:</strong></div>';
                        detailsHtml += '<div style="margin-left: 15px;">• ';
                        const intersectionNames = intersections.map(intersection => intersection.name || intersection.id).join('、');
                        detailsHtml += intersectionNames + '</div>';
                    }
                }

                // 显示路段分析详情（如果存在）
                const edgeAnalysis = config?.analysis_config?.edge_analysis;
                if (edgeAnalysis?.enabled) {
                    const selectedEdges = edgeAnalysis.selected_edges_with_names;
                    if (selectedEdges && selectedEdges.length > 0) {
                        detailsHtml += '<div><strong>分析路段详情:</strong></div>';
                        detailsHtml += '<div style="margin-left: 15px;">• ';
                        const edgeNames = selectedEdges.map(edge => edge.name || edge.id).join('、');
                        detailsHtml += edgeNames + '</div>';
                    }
                }

            } catch (error) {
                console.warn('生成详细配置显示时出错:', error);
            }

            detailsHtml += '</div>';
            return detailsHtml;
        }

        // 添加对比标题
        function addComparisonHeader(compareId) {
            // 更新对比方案信息显示
            const comparisonSchemesInfo = document.getElementById('comparisonSchemesInfo');
            if (comparisonSchemesInfo) {
                comparisonSchemesInfo.textContent = `当前对比方案：${compareId}`;
                comparisonSchemesInfo.style.color = '#30abe8';
                comparisonSchemesInfo.style.fontWeight = 'bold';
            }
        }
        
        // 创建对比图表
        function createComparisonCharts(currentData, compareData) {
            console.log("创建对比图表，当前方案ID:", currentData.simulation_id, "对比方案ID:", compareData.simulation_id);

            // 兼容新/旧格式，提取四类指标
            function extractMetrics(data) {
                if (data && data.simulation_results) {
                    if (data.simulation_results.network_metrics) {
                        // 新格式
                        return JSON.parse(JSON.stringify(data.simulation_results.network_metrics));
                    } else {
                        // 旧格式
                        return JSON.parse(JSON.stringify({
                            pedestrian_metrics: data.simulation_results.pedestrian_metrics || {},
                            vehicle_metrics: data.simulation_results.vehicle_metrics || {},
                            vip_vehicle_metrics: data.simulation_results.vip_vehicle_metrics || {},
                            venue_area_metrics: data.simulation_results.venue_area_metrics || {}
                        }));
                    }
                }
                // 如果数据格式异常，返回空对象
                return {
                    vehicle_metrics: {},
                    pedestrian_metrics: {},
                    vip_vehicle_metrics: {},
                    venue_area_metrics: {}
                };
            }

            const currentMetrics = extractMetrics(currentData);
            const compareMetrics = extractMetrics(compareData);
            
            // 确保所有指标都有默认值
            ensureAllMetricsDefaults(currentMetrics);
            ensureAllMetricsDefaults(compareMetrics);

            // 使用对比图表函数创建对比图表
            createComparisonTravelTimeChart(currentMetrics, compareMetrics, compareData.simulation_id);
            createComparisonWaitingTimeChart(currentMetrics, compareMetrics, compareData.simulation_id);
            createComparisonWaitingCountChart(currentMetrics, compareMetrics, compareData.simulation_id);
            createComparisonTimeLossDelayChart(currentMetrics, compareMetrics, compareData.simulation_id);
        }
    </script>
</body>
</html>
