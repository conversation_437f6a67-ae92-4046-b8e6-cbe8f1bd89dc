import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

class HistoryManager:
    """历史方案管理器"""

    def __init__(self):
        self.history_dir = 'sumo_data/history'
        self.history_file = os.path.join(self.history_dir, 'saved_schemes.json')
        # 添加仿真结果目录 - 使用绝对路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        self.simulation_results_dir = os.path.join(current_dir, 'sumo', 'output', '2023', 'activity')
        self._ensure_history_directory()

    def _ensure_history_directory(self):
        """确保历史目录存在"""
        os.makedirs(self.history_dir, exist_ok=True)
        if not os.path.exists(self.history_file):
            self._save_history_data([])

    def _load_history_data(self) -> List[Dict[str, Any]]:
        """加载历史数据"""
        try:
            with open(self.history_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return []

    def _load_simulation_results_data(self) -> List[Dict[str, Any]]:
        """加载仿真结果目录中的数据"""
        simulation_results = []

        if not os.path.exists(self.simulation_results_dir):
            return simulation_results

        try:
            # 遍历仿真结果目录中的所有JSON文件
            for filename in os.listdir(self.simulation_results_dir):
                if filename.startswith('simulation_result_') and filename.endswith('.json'):
                    file_path = os.path.join(self.simulation_results_dir, filename)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        # 转换为历史方案格式
                        scheme_record = {
                            'id': data.get('simulation_id', filename.replace('.json', '')),
                            'name': f"仿真结果_{data.get('simulation_id', 'unknown')}",
                            'description': '从仿真结果目录加载',
                            'created_time': data.get('start_time', datetime.now().isoformat()),
                            'config': data.get('config', {}),
                            'config_summary': data.get('config_summary', {}),
                            'simulation_results': data.get('simulation_results', {}),
                            'source': 'simulation_results'  # 标记数据来源
                        }

                        # 如果没有config_summary，尝试生成
                        if not scheme_record['config_summary'] and scheme_record['config']:
                            scheme_record['config_summary'] = self._generate_config_summary(scheme_record['config'])

                        simulation_results.append(scheme_record)

                    except (json.JSONDecodeError, Exception) as e:
                        print(f"警告：无法加载仿真结果文件 {filename}: {str(e)}")
                        continue

        except Exception as e:
            print(f"警告：无法访问仿真结果目录 {self.simulation_results_dir}: {str(e)}")

        return simulation_results

    def _save_history_data(self, data: List[Dict[str, Any]]):
        """保存历史数据"""
        with open(self.history_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

    def _generate_scheme_id(self) -> str:
        """生成方案ID"""
        return datetime.now().strftime("%y%m%d_%H%M%S")

    def _get_default_simulation_results(self) -> Dict[str, Any]:
        """获取默认的仿真结果结构，确保与标准格式一致"""
        return {
            "network_metrics": {
                "pedestrian_metrics": {
                    "average_travel_time": 0.0,
                    "average_waiting_time": 0.0,
                    "average_waiting_count": 0.0,
                    "average_time_loss": 0.0
                },
                "vehicle_metrics": {
                    "average_travel_time": 0.0,
                    "average_waiting_time": 0.0,
                    "average_waiting_count": 0.0,
                    "average_time_loss": 0.0
                },
                "vip_vehicle_metrics": {
                    "average_travel_time": 0,
                    "average_waiting_time": 0,
                    "average_waiting_count": 0,
                    "average_time_loss": 0
                },
                "venue_area_metrics": {
                    "average_pedestrian_travel_time": 0,
                    "average_pedestrian_delay": 0,
                    "average_vehicle_travel_time": 0,
                    "average_vehicle_delay": 0
                }
            },
            "selected_edge_metrics": {
                "pedestrian_metrics": {
                    "total_departed": 0.0,
                    "total_arrived": 0.0,
                    "total_entered": 0.0,
                    "avg_traveltime": 0,
                    "avg_waitingTime": 0,
                    "avg_speed": 0.0,
                    "avg_timeLoss": 0,
                    "avg_occupancy": 0.0
                },
                "vehicle_metrics": {
                    "total_departed": 0,
                    "total_arrived": 0.0,
                    "total_entered": 0.0,
                    "avg_traveltime": 0.0,
                    "avg_waitingTime": 0.0,
                    "avg_speed": 0.0,
                    "avg_timeLoss": 0.0,
                    "avg_occupancy": 0.0
                }
            }
        }

    def _ensure_complete_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        确保配置信息完整，补充缺失的配置项

        参数:
        config: dict - 输入的配置（可能不完整）

        返回:
        dict - 完整的配置结构
        """
        # 默认的完整配置结构
        default_config = {
            "network_config": {
                "type": "predefined",
                "file_path": None,
                "entrance_plan": "仅开放东侧出入口",
                "road_restriction": {
                    "enabled": False,
                    "vehicle_restricted_edges": [],
                    "pedestrian_restricted_edges": [],
                    "vehicle_restricted_edges_with_names": [],
                    "pedestrian_restricted_edges_with_names": []
                }
            },
            "signal_config": {
                "type": "predefined",
                "file_path": None,
                "optimization": {
                    "enabled": False,
                    "selected_intersections": [],
                    "selected_intersections_with_names": []
                }
            },
            "traffic_config": {
                "type": "predefined",
                "file_path": None,
                "scenario": "进场",
                "vehicle_type": "仅一般车辆",
                "traffic_scale": "medium",
                "vip_priority": {
                    "enabled": False
                }
            },
            "analysis_config": {
                "edge_analysis": {
                    "enabled": False,
                    "selected_edges": [],
                    "selected_edges_with_names": []
                }
            }
        }

        if config is None:
            return default_config

        # 深度合并，确保所有必要字段都存在
        def deep_merge(default_dict, input_dict):
            """深度合并字典，保留输入值但确保结构完整"""
            result = default_dict.copy()

            for key, value in input_dict.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = deep_merge(result[key], value)
                else:
                    result[key] = value

            return result

        return deep_merge(default_config, config)

    def _ensure_complete_simulation_results(self, simulation_results: dict = None) -> Dict[str, Any]:
        """
        确保simulation_results包含完整的结构

        参数:
        simulation_results: dict - 输入的仿真结果（可能不完整）

        返回:
        dict - 完整的仿真结果结构
        """
        # 获取默认的完整结构
        complete_results = self._get_default_simulation_results()

        if simulation_results is None:
            return complete_results

        # 深度合并，确保所有必要字段都存在
        def deep_merge(default_dict, input_dict):
            """深度合并字典，保留输入值但确保结构完整"""
            result = default_dict.copy()

            for key, value in input_dict.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = deep_merge(result[key], value)
                else:
                    result[key] = value

            return result

        return deep_merge(complete_results, simulation_results)

    # === 只修改这里的方法签名和实现 ===
    def save_scheme(self, config: Dict[str, Any], name: str = None, description: str = None, simulation_results: dict = None) -> Dict[str, Any]:
        """
        保存配置方案

        参数:
        config: dict - 配置数据
        name: str - 方案名称（可选）
        description: str - 方案描述（可选）
        simulation_results: dict - 仿真结果（可选）

        返回:
        dict - 保存结果
        """
        try:
            scheme_id = self._generate_scheme_id()
            timestamp = datetime.now().isoformat()
            if not name:
                name = f"方案_{scheme_id}"

            # 确保配置信息完整
            complete_config = self._ensure_complete_config(config)
            
            # 确保所有保存的配置都包含完整的simulation_results结构
            complete_simulation_results = self._ensure_complete_simulation_results(simulation_results)

            # 使用与backend\sumo\output\2023\activity相同的格式
            scheme_record = {
                'simulation_id': scheme_id,  # 改为simulation_id
                'config': complete_config,
                'config_summary': self._generate_config_summary(complete_config),
                'start_time': timestamp,  # 改为start_time
                'simulation_results': complete_simulation_results
            }

            # 为了兼容历史方案列表显示，同时保存旧格式字段
            history_record = {
                'id': scheme_id,
                'name': name,
                'description': description or '',
                'created_time': timestamp,
                'config': complete_config,
                'config_summary': self._generate_config_summary(complete_config),
                'simulation_results': complete_simulation_results
            }

            # 保存到历史列表（使用旧格式以保持兼容性）
            history_data = self._load_history_data()
            history_data.insert(0, history_record)
            if len(history_data) > 50:
                history_data = history_data[:50]
            self._save_history_data(history_data)

            # 保存单独的方案文件（使用新格式，与activity目录一致）
            scheme_file = os.path.join(self.history_dir, f'scheme_{scheme_id}.json')
            with open(scheme_file, 'w', encoding='utf-8') as f:
                json.dump(scheme_record, f, ensure_ascii=False, indent=4)  # 使用4个空格缩进，与activity目录一致

            # 额外保存一份到backend\sumo\output\2023\activity目录（与仿真结果格式完全一致）
            try:
                # 获取当前脚本目录
                current_dir = os.path.dirname(os.path.abspath(__file__))
                activity_dir = os.path.join(current_dir, 'sumo', 'output', '2023', 'activity')
                os.makedirs(activity_dir, exist_ok=True)

                # 保存到activity目录
                activity_file = os.path.join(activity_dir, f'simulation_result_{scheme_id}.json')
                with open(activity_file, 'w', encoding='utf-8') as f:
                    json.dump(scheme_record, f, ensure_ascii=False, indent=4)

                print(f"配置方案已额外保存到: {activity_file}")

            except Exception as e:
                print(f"警告：保存到activity目录时出错: {str(e)}")
                # 不影响主流程，继续执行

            return {
                'success': True,
                'scheme_id': scheme_id,
                'message': f'方案 "{name}" 保存成功',
                'scheme_file': scheme_file
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'保存方案失败: {str(e)}'
            }

    def get_schemes_list(self) -> List[Dict[str, Any]]:
        """获取历史方案列表（包括保存的方案和仿真结果）"""
        # 加载保存的历史方案
        history_data = self._load_history_data()

        # 加载仿真结果目录中的数据
        simulation_results_data = self._load_simulation_results_data()

        # 合并两个数据源
        all_schemes = []

        # 添加历史方案
        for scheme in history_data:
            all_schemes.append({
                'id': scheme['id'],
                'name': scheme['name'],
                'description': scheme['description'],
                'created_time': scheme['created_time'],
                'config_summary': scheme['config_summary'],
                'source': scheme.get('source', 'history')
            })

        # 添加仿真结果
        for scheme in simulation_results_data:
            all_schemes.append({
                'id': scheme['id'],
                'name': scheme['name'],
                'description': scheme['description'],
                'created_time': scheme['created_time'],
                'config_summary': scheme['config_summary'],
                'source': scheme.get('source', 'simulation_results')
            })

        # 按创建时间排序（最新的在前面）
        all_schemes.sort(key=lambda x: x['created_time'], reverse=True)

        return all_schemes

    def get_scheme_by_id(self, scheme_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取方案详情（从历史方案和仿真结果中查找）"""
        # 首先从历史方案中查找
        history_data = self._load_history_data()
        for scheme in history_data:
            if scheme['id'] == scheme_id:
                # 确保返回的数据结构完整
                complete_scheme = scheme.copy()
                complete_scheme['config'] = self._ensure_complete_config(scheme.get('config', {}))
                complete_scheme['simulation_results'] = self._ensure_complete_simulation_results(scheme.get('simulation_results', {}))
                return complete_scheme

        # 如果历史方案中没有找到，从仿真结果中查找
        simulation_results_data = self._load_simulation_results_data()
        for scheme in simulation_results_data:
            if scheme['id'] == scheme_id:
                # 确保返回的数据结构完整
                complete_scheme = scheme.copy()
                complete_scheme['config'] = self._ensure_complete_config(scheme.get('config', {}))
                complete_scheme['simulation_results'] = self._ensure_complete_simulation_results(scheme.get('simulation_results', {}))
                return complete_scheme

        return None

    def delete_scheme(self, scheme_id: str) -> Dict[str, Any]:
        """删除方案"""
        try:
            history_data = self._load_history_data()
            scheme_found = False
            updated_data = []
            for scheme in history_data:
                if scheme['id'] == scheme_id:
                    scheme_found = True
                else:
                    updated_data.append(scheme)
            if not scheme_found:
                return {
                    'success': False,
                    'error': f'未找到ID为 {scheme_id} 的方案'
                }
            
            # 更新历史列表
            self._save_history_data(updated_data)
            
            # 删除单独的方案文件
            scheme_file = os.path.join(self.history_dir, f'scheme_{scheme_id}.json')
            if os.path.exists(scheme_file):
                os.remove(scheme_file)
            
            # 删除对应的仿真结果文件
            try:
                current_dir = os.path.dirname(os.path.abspath(__file__))
                activity_dir = os.path.join(current_dir, 'sumo', 'output', '2023', 'activity')
                activity_file = os.path.join(activity_dir, f'simulation_result_{scheme_id}.json')
                if os.path.exists(activity_file):
                    os.remove(activity_file)
                    print(f"已删除仿真结果文件: {activity_file}")
            except Exception as e:
                print(f"警告：删除仿真结果文件时出错: {str(e)}")
                # 不影响主流程，继续执行
            
            return {
                'success': True,
                'message': f'方案 {scheme_id} 删除成功'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'删除方案失败: {str(e)}'
            }

    def update_scheme_info(self, scheme_id: str, name: str = None, description: str = None) -> Dict[str, Any]:
        """更新方案信息（名称和描述）"""
        try:
            history_data = self._load_history_data()
            scheme_found = False
            for scheme in history_data:
                if scheme['id'] == scheme_id:
                    if name is not None:
                        scheme['name'] = name
                    if description is not None:
                        scheme['description'] = description
                    scheme_found = True
                    break
            if not scheme_found:
                return {
                    'success': False,
                    'error': f'未找到ID为 {scheme_id} 的方案'
                }
            self._save_history_data(history_data)
            return {
                'success': True,
                'message': f'方案 {scheme_id} 信息更新成功'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'更新方案信息失败: {str(e)}'
            }

    def _generate_config_summary(self, config: Dict[str, Any]) -> Dict[str, str]:
        """生成配置摘要"""
        try:
            network_config = config.get('network_config', {})
            network_type = "预设路网" if network_config.get('type') == 'predefined' else "自定义路网"
            entrance_plan = network_config.get('entrance_plan', '未知')
            road_restriction = network_config.get('road_restriction', {})
            restriction_text = ""
            if road_restriction.get('enabled'):
                # 正确统计车辆限行和行人限行路段数量
                vehicle_edges = road_restriction.get('vehicle_restricted_edges', [])
                pedestrian_edges = road_restriction.get('pedestrian_restricted_edges', [])
                restricted_count = len(vehicle_edges) + len(pedestrian_edges)
                restriction_text = f" + 道路限行({restricted_count}个路段)"
            network_summary = f"{network_type} - {entrance_plan}{restriction_text}"

            signal_config = config.get('signal_config', {})
            signal_type = "预设配时" if signal_config.get('type') == 'predefined' else "自定义配时"
            optimization = signal_config.get('optimization', {})
            optimization_text = ""
            if optimization.get('enabled'):
                intersection_count = len(optimization.get('selected_intersections', []))
                optimization_text = f" + 自定义优化({intersection_count}个交叉口)"
            signal_summary = f"{signal_type}{optimization_text}"

            traffic_config = config.get('traffic_config', {})
            traffic_type = "预设需求" if traffic_config.get('type') == 'predefined' else "自定义需求"
            scenario = traffic_config.get('scenario', '未知')
            vehicle_type = traffic_config.get('vehicle_type', '未知')
            vip_priority = traffic_config.get('vip_priority', {})
            vip_text = " + 贵宾专车" if vip_priority.get('enabled') else ""
            traffic_summary = f"{traffic_type} - {scenario}场景 + {vehicle_type}{vip_text}"

            return {
                'network': network_summary,
                'signal': signal_summary,
                'traffic': traffic_summary
            }
        except Exception as e:
            return {
                'network': '配置解析失败',
                'signal': '配置解析失败',
                'traffic': '配置解析失败'
            }
