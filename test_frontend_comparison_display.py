#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试前端方案对比显示效果
"""

import json
import os

def test_frontend_comparison_display():
    """测试前端方案对比显示效果"""
    
    print("=== 测试前端方案对比显示效果 ===")
    
    # 检查测试方案文件
    test_file = 'frontend/test_scheme_250801_163628.json'
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    # 读取测试方案文件
    with open(test_file, 'r', encoding='utf-8') as f:
        scheme_data = json.load(f)
    
    print(f"✅ 测试方案文件存在: {scheme_data['simulation_id']}")
    print(f"方案名称: {scheme_data['name']}")
    print(f"方案描述: {scheme_data['description']}")
    
    # 检查配置摘要
    config_summary = scheme_data.get('config_summary', {})
    
    print("\n=== 配置摘要内容 ===")
    print(f"网络: {config_summary.get('network', '未知')}")
    print(f"信号: {config_summary.get('signal', '未知')}")
    print(f"交通: {config_summary.get('traffic', '未知')}")
    
    # 分析配置摘要中的具体信息
    print("\n=== 具体信息分析 ===")
    
    network_summary = config_summary.get('network', '')
    signal_summary = config_summary.get('signal', '')
    
    # 检查道路名称
    road_names = ['易城街', '荣乌高速', '锦朋路', '海岳大街']
    found_roads = [name for name in road_names if name in network_summary]
    
    print(f"包含的具体道路名称: {', '.join(found_roads) if found_roads else '无'}")
    
    # 检查交叉口名称
    intersection_names = ['交叉口11363432', '交叉口10309271185', '交叉口11155676', '交叉口11411026']
    found_intersections = [name for name in intersection_names if name in signal_summary]
    
    print(f"包含的具体交叉口名称: {', '.join(found_intersections) if found_intersections else '无'}")
    
    # 检查是否有省略
    has_etc_network = '等' in network_summary
    has_etc_signal = '等' in signal_summary
    
    print(f"\n=== 省略检查 ===")
    print(f"网络摘要包含'等'字样: {'是' if has_etc_network else '否'}")
    print(f"信号摘要包含'等'字样: {'是' if has_etc_signal else '否'}")
    
    if not has_etc_network and not has_etc_signal:
        print("✅ 配置摘要不包含省略，显示完整信息")
    else:
        print("⚠️  配置摘要仍包含省略")
    
    # 预期的前端显示效果
    print(f"\n=== 预期的前端显示效果 ===")
    print("在'当前对比方案'区域应该显示:")
    print(f"  当前对比方案：{scheme_data['simulation_id']}")
    print(f"  路网: {network_summary}")
    print(f"  信号: {signal_summary}")
    
    print("\n在'对比方案摘要列表'区域应该显示:")
    print(f"  方案名称: {scheme_data['name']}")
    print(f"  方案ID: {scheme_data['simulation_id']}")
    print(f"  网络配置: {network_summary}")
    print(f"  信号配置: {signal_summary}")
    print(f"  交通配置: {config_summary.get('traffic', '未知')}")
    
    print(f"\n🎯 测试建议:")
    print("1. 打开 http://localhost:8000/final-test.html")
    print("2. 点击'方案对比'按钮")
    print("3. 选择文件加载测试方案")
    print("4. 检查'当前对比方案'区域是否显示具体道路名和交叉口名")
    print("5. 检查是否不包含'等'字样的省略")

if __name__ == '__main__':
    test_frontend_comparison_display()
