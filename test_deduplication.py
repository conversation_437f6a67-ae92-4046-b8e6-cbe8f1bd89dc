#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试去重功能
"""

import os
import sys
import json

# 添加backend目录到路径
sys.path.append('backend')

from history_manager import HistoryManager

def test_deduplication():
    """测试去重功能"""
    print("=== 测试方案列表去重功能 ===\n")
    
    # 创建历史管理器
    history_manager = HistoryManager()
    
    print("1. 获取当前方案列表...")
    schemes = history_manager.get_schemes_list()
    
    print(f"总方案数: {len(schemes)}")
    
    # 检查是否有重复的ID
    scheme_ids = []
    duplicate_ids = []
    
    for scheme in schemes:
        scheme_id = scheme['id']
        if scheme_id in scheme_ids:
            duplicate_ids.append(scheme_id)
        else:
            scheme_ids.append(scheme_id)
    
    if duplicate_ids:
        print(f"❌ 发现重复的方案ID: {duplicate_ids}")
    else:
        print("✅ 没有发现重复的方案ID")
    
    print("\n2. 方案列表详情:")
    for i, scheme in enumerate(schemes, 1):
        print(f"{i}. ID: {scheme['id']}")
        print(f"   名称: {scheme['name']}")
        print(f"   描述: {scheme['description']}")
        print(f"   来源: {scheme['source']}")
        print(f"   时间: {scheme['created_time']}")
        print()
    
    # 检查特定的重复情况
    print("3. 检查特定时间的方案...")
    target_time = "2025-08-04T11:20:35"
    matching_schemes = [s for s in schemes if target_time in s['created_time']]
    
    if len(matching_schemes) > 1:
        print(f"❌ 发现同一时间的多个方案:")
        for scheme in matching_schemes:
            print(f"   - {scheme['id']}: {scheme['name']} ({scheme['source']})")
    elif len(matching_schemes) == 1:
        print(f"✅ 同一时间只有一个方案: {matching_schemes[0]['id']}")
    else:
        print("ℹ️ 没有找到该时间的方案")
    
    print("\n4. 检查历史文件和activity目录的对应关系...")
    
    # 检查历史文件
    history_data = history_manager._load_history_data()
    print(f"历史文件中的方案数: {len(history_data)}")
    
    # 检查activity目录
    simulation_data = history_manager._load_simulation_results_data()
    print(f"仿真结果目录中的方案数: {len(simulation_data)}")
    
    # 找出重叠的ID
    history_ids = set(scheme['id'] for scheme in history_data)
    simulation_ids = set(scheme['id'] for scheme in simulation_data)
    overlap_ids = history_ids.intersection(simulation_ids)
    
    if overlap_ids:
        print(f"✅ 重叠的方案ID (已去重): {list(overlap_ids)}")
    else:
        print("ℹ️ 没有重叠的方案ID")

if __name__ == "__main__":
    test_deduplication()
