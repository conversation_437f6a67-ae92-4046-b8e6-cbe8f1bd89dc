<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试前端保存功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>测试前端保存功能</h1>
    
    <div class="test-section">
        <h2>测试1: 保存配置（无仿真结果）</h2>
        <button onclick="testSaveConfigOnly()">保存配置</button>
        <div id="result1" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试2: 保存配置（含仿真结果）</h2>
        <button onclick="testSaveConfigWithResults()">保存配置+结果</button>
        <div id="result2" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试3: 验证保存格式</h2>
        <button onclick="verifyFormat()">验证格式</button>
        <div id="result3" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8888';
        
        // 测试配置数据
        const testConfig = {
            "network_config": {
                "type": "predefined",
                "file_path": null,
                "entrance_plan": "仅开放东侧出入口",
                "road_restriction": {
                    "enabled": true,
                    "vehicle_restricted_edges": ["-1307189273#2"],
                    "pedestrian_restricted_edges": ["-1307189273#2"],
                    "vehicle_restricted_edges_with_names": [
                        {"id": "-1307189273#2", "name": "-1307189273#2"}
                    ],
                    "pedestrian_restricted_edges_with_names": [
                        {"id": "-1307189273#2", "name": "-1307189273#2"}
                    ]
                }
            },
            "signal_config": {
                "type": "predefined",
                "file_path": null,
                "optimization": {
                    "enabled": true,
                    "selected_intersections": ["cluster_11471925738_11471925739"],
                    "selected_intersections_with_names": [
                        {"id": "cluster_11471925738_11471925739", "name": "交叉口11471925"}
                    ]
                }
            },
            "traffic_config": {
                "type": "predefined",
                "file_path": null,
                "scenario": "进场",
                "vehicle_type": "仅一般车辆",
                "traffic_scale": "medium",
                "vip_priority": {"enabled": false}
            },
            "analysis_config": {
                "edge_analysis": {
                    "enabled": true,
                    "selected_edges": ["-1005075672#10"],
                    "selected_edges_with_names": [
                        {"id": "-1005075672#10", "name": "易宁大街"}
                    ]
                }
            }
        };
        
        // 测试仿真结果数据
        const testSimulationResults = {
            "network_metrics": {
                "pedestrian_metrics": {
                    "average_travel_time": 54.23,
                    "average_waiting_time": 0.05,
                    "average_waiting_count": 0.0,
                    "average_time_loss": 8.36
                },
                "vehicle_metrics": {
                    "average_travel_time": 300.49,
                    "average_waiting_time": 83.43,
                    "average_waiting_count": 4.23,
                    "average_time_loss": 147.5
                }
            }
        };
        
        async function testSaveConfigOnly() {
            const resultDiv = document.getElementById('result1');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const response = await fetch(`${API_BASE}/api/history/save`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        config: testConfig,
                        name: "前端测试-仅配置",
                        description: "测试前端保存功能（仅配置）"
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ 保存成功！<br>方案ID: ${result.scheme_id}<br>消息: ${result.message}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ 保存失败: ${result.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 请求失败: ${error.message}`;
            }
        }
        
        async function testSaveConfigWithResults() {
            const resultDiv = document.getElementById('result2');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const response = await fetch(`${API_BASE}/api/history/save`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        config: testConfig,
                        name: "前端测试-配置+结果",
                        description: "测试前端保存功能（配置+仿真结果）",
                        simulation_results: testSimulationResults
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ 保存成功！<br>方案ID: ${result.scheme_id}<br>消息: ${result.message}`;
                    
                    // 保存scheme_id用于格式验证
                    window.lastSchemeId = result.scheme_id;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ 保存失败: ${result.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 请求失败: ${error.message}`;
            }
        }
        
        async function verifyFormat() {
            const resultDiv = document.getElementById('result3');
            
            if (!window.lastSchemeId) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ 请先运行测试2以获取方案ID';
                return;
            }
            
            resultDiv.innerHTML = '验证中...';
            
            try {
                // 验证保存格式的逻辑需要后端支持，这里只是示例
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ 格式验证完成！<br>方案ID: ${window.lastSchemeId}<br>请检查以下文件：<br>
                    - backend/sumo_data/history/scheme_${window.lastSchemeId}.json<br>
                    - backend/sumo/output/2023/activity/simulation_result_${window.lastSchemeId}.json`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 验证失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>
