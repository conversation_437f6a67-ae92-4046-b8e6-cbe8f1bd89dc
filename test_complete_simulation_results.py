#!/usr/bin/env python3
"""
测试保存配置功能，确保所有保存的配置都包含完整的simulation_results结构
"""

import requests
import json
import os
from datetime import datetime

def test_save_without_simulation_results():
    """测试保存配置（不提供simulation_results）"""
    print("=== 测试1: 保存配置（不提供simulation_results）===")
    
    test_config = {
        "network_config": {
            "type": "predefined",
            "file_path": None,
            "entrance_plan": "仅开放东侧出入口",
            "road_restriction": {
                "enabled": True,
                "vehicle_restricted_edges": ["-1307189273#2"],
                "pedestrian_restricted_edges": ["-1307189273#2"],
                "vehicle_restricted_edges_with_names": [
                    {"id": "-1307189273#2", "name": "-1307189273#2"}
                ],
                "pedestrian_restricted_edges_with_names": [
                    {"id": "-1307189273#2", "name": "-1307189273#2"}
                ]
            }
        },
        "signal_config": {
            "type": "predefined",
            "file_path": None,
            "optimization": {
                "enabled": True,
                "selected_intersections": ["cluster_11471925738_11471925739"],
                "selected_intersections_with_names": [
                    {"id": "cluster_11471925738_11471925739", "name": "交叉口11471925"}
                ]
            }
        },
        "traffic_config": {
            "type": "predefined",
            "file_path": None,
            "scenario": "进场",
            "vehicle_type": "仅一般车辆",
            "traffic_scale": "medium",
            "vip_priority": {"enabled": False}
        },
        "analysis_config": {
            "edge_analysis": {
                "enabled": True,
                "selected_edges": ["-1005075672#10"],
                "selected_edges_with_names": [
                    {"id": "-1005075672#10", "name": "易宁大街"}
                ]
            }
        }
    }
    
    try:
        response = requests.post("http://localhost:8888/api/history/save", json={
            "config": test_config,
            "name": "测试默认simulation_results",
            "description": "测试不提供simulation_results时的默认结构"
            # 注意：这里故意不提供simulation_results
        })
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                scheme_id = result['scheme_id']
                print(f"✅ 保存成功，方案ID: {scheme_id}")
                return scheme_id
            else:
                print(f"❌ 保存失败: {result['error']}")
        else:
            print(f"❌ HTTP请求失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
    
    return None

def test_save_with_simulation_results():
    """测试保存配置（提供simulation_results）"""
    print("\n=== 测试2: 保存配置（提供simulation_results）===")
    
    test_config = {
        "network_config": {
            "type": "predefined",
            "file_path": None,
            "entrance_plan": "仅开放东侧出入口",
            "road_restriction": {
                "enabled": True,
                "vehicle_restricted_edges": ["-1307189273#2"],
                "pedestrian_restricted_edges": ["-1307189273#2"],
                "vehicle_restricted_edges_with_names": [
                    {"id": "-1307189273#2", "name": "-1307189273#2"}
                ],
                "pedestrian_restricted_edges_with_names": [
                    {"id": "-1307189273#2", "name": "-1307189273#2"}
                ]
            }
        },
        "signal_config": {
            "type": "predefined",
            "file_path": None,
            "optimization": {
                "enabled": True,
                "selected_intersections": ["cluster_11471925738_11471925739"],
                "selected_intersections_with_names": [
                    {"id": "cluster_11471925738_11471925739", "name": "交叉口11471925"}
                ]
            }
        },
        "traffic_config": {
            "type": "predefined",
            "file_path": None,
            "scenario": "进场",
            "vehicle_type": "仅一般车辆",
            "traffic_scale": "medium",
            "vip_priority": {"enabled": False}
        },
        "analysis_config": {
            "edge_analysis": {
                "enabled": True,
                "selected_edges": ["-1005075672#10"],
                "selected_edges_with_names": [
                    {"id": "-1005075672#10", "name": "易宁大街"}
                ]
            }
        }
    }
    
    # 提供自定义的simulation_results
    custom_simulation_results = {
        "network_metrics": {
            "pedestrian_metrics": {
                "average_travel_time": 100.5,
                "average_waiting_time": 5.2,
                "average_waiting_count": 2.1,
                "average_time_loss": 15.8
            },
            "vehicle_metrics": {
                "average_travel_time": 250.3,
                "average_waiting_time": 45.7,
                "average_waiting_count": 3.2,
                "average_time_loss": 89.4
            },
            "vip_vehicle_metrics": {
                "average_travel_time": 180.2,
                "average_waiting_time": 12.5,
                "average_waiting_count": 1.1,
                "average_time_loss": 25.3
            },
            "venue_area_metrics": {
                "average_pedestrian_travel_time": 75.6,
                "average_pedestrian_delay": 8.9,
                "average_vehicle_travel_time": 120.4,
                "average_vehicle_delay": 22.1
            }
        },
        "selected_edge_metrics": {
            "pedestrian_metrics": {
                "total_departed": 150.0,
                "total_arrived": 145.0,
                "total_entered": 160.0,
                "avg_traveltime": 85,
                "avg_waitingTime": 12,
                "avg_speed": 1.2,
                "avg_timeLoss": 18,
                "avg_occupancy": 0.15
            },
            "vehicle_metrics": {
                "total_departed": 80,
                "total_arrived": 75.0,
                "total_entered": 85.0,
                "avg_traveltime": 180.5,
                "avg_waitingTime": 35.2,
                "avg_speed": 25.8,
                "avg_timeLoss": 45.7,
                "avg_occupancy": 0.08
            }
        }
    }
    
    try:
        response = requests.post("http://localhost:8888/api/history/save", json={
            "config": test_config,
            "name": "测试自定义simulation_results",
            "description": "测试提供自定义simulation_results",
            "simulation_results": custom_simulation_results
        })
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                scheme_id = result['scheme_id']
                print(f"✅ 保存成功，方案ID: {scheme_id}")
                return scheme_id
            else:
                print(f"❌ 保存失败: {result['error']}")
        else:
            print(f"❌ HTTP请求失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
    
    return None

def verify_simulation_results_structure(scheme_id, test_name):
    """验证保存的simulation_results结构"""
    print(f"\n=== 验证 {test_name} 的simulation_results结构 ===")
    
    # 检查activity目录文件
    activity_file = f"backend/sumo/output/2023/activity/simulation_result_{scheme_id}.json"
    if not os.path.exists(activity_file):
        print(f"❌ Activity文件不存在: {activity_file}")
        return False
    
    with open(activity_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 检查是否包含simulation_results
    if 'simulation_results' not in data:
        print("❌ 缺少simulation_results字段")
        return False
    
    simulation_results = data['simulation_results']
    
    # 检查必要的顶级字段
    required_top_fields = ['network_metrics', 'selected_edge_metrics']
    for field in required_top_fields:
        if field not in simulation_results:
            print(f"❌ simulation_results缺少字段: {field}")
            return False
    
    # 检查network_metrics结构
    network_metrics = simulation_results['network_metrics']
    required_network_fields = ['pedestrian_metrics', 'vehicle_metrics', 'vip_vehicle_metrics', 'venue_area_metrics']
    for field in required_network_fields:
        if field not in network_metrics:
            print(f"❌ network_metrics缺少字段: {field}")
            return False
    
    # 检查selected_edge_metrics结构
    selected_edge_metrics = simulation_results['selected_edge_metrics']
    required_edge_fields = ['pedestrian_metrics', 'vehicle_metrics']
    for field in required_edge_fields:
        if field not in selected_edge_metrics:
            print(f"❌ selected_edge_metrics缺少字段: {field}")
            return False
    
    # 检查pedestrian_metrics字段
    pedestrian_metrics = network_metrics['pedestrian_metrics']
    required_pedestrian_fields = ['average_travel_time', 'average_waiting_time', 'average_waiting_count', 'average_time_loss']
    for field in required_pedestrian_fields:
        if field not in pedestrian_metrics:
            print(f"❌ pedestrian_metrics缺少字段: {field}")
            return False
    
    # 检查vehicle_metrics字段
    vehicle_metrics = network_metrics['vehicle_metrics']
    required_vehicle_fields = ['average_travel_time', 'average_waiting_time', 'average_waiting_count', 'average_time_loss']
    for field in required_vehicle_fields:
        if field not in vehicle_metrics:
            print(f"❌ vehicle_metrics缺少字段: {field}")
            return False
    
    print("✅ simulation_results结构完整且正确")
    print(f"  - network_metrics: {len(network_metrics)} 个子字段")
    print(f"  - selected_edge_metrics: {len(selected_edge_metrics)} 个子字段")
    
    return True

def main():
    print("=== 测试完整simulation_results结构保存功能 ===\n")
    
    # 测试1: 不提供simulation_results
    scheme_id_1 = test_save_without_simulation_results()
    if scheme_id_1:
        verify_simulation_results_structure(scheme_id_1, "默认simulation_results")
    
    # 测试2: 提供自定义simulation_results
    scheme_id_2 = test_save_with_simulation_results()
    if scheme_id_2:
        verify_simulation_results_structure(scheme_id_2, "自定义simulation_results")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
