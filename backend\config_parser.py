import os
import json
from typing import Dict, List, Any, Tuple

class ConfigParser:
    """配置解析器，负责验证新的JSON配置格式并提供配置访问接口"""
    
    def __init__(self):
        # 获取当前文件所在目录（backend目录）
        current_dir = os.path.dirname(os.path.abspath(__file__))
        self.base_dir = os.path.join(current_dir, 'sumo_data')
        self.template_dir = os.path.join(self.base_dir, 'templates')
        
    def validate_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证配置的有效性
        
        参数:
        config: dict - 新格式的JSON配置
        
        返回:
        dict - 验证结果 {'valid': bool, 'errors': list}
        """
        errors = []
        
        try:
            # 1. 验证顶层结构
            required_sections = ['network_config', 'signal_config', 'traffic_config']
            for section in required_sections:
                if section not in config:
                    errors.append(f"缺少必需的配置模块: {section}")
            
            if errors:
                return {'valid': False, 'errors': errors}
                
            # 2. 验证网络配置
            network_errors = self._validate_network_config(config['network_config'])
            errors.extend(network_errors)
            
            # 3. 验证信号配置
            signal_errors = self._validate_signal_config(config['signal_config'])
            errors.extend(signal_errors)
            
            # 4. 验证交通配置  
            traffic_errors = self._validate_traffic_config(config['traffic_config'])
            errors.extend(traffic_errors)
            
            # 5. 验证业务逻辑约束
            logic_errors = self._validate_business_logic(config)
            errors.extend(logic_errors)
            
            return {'valid': len(errors) == 0, 'errors': errors}
            
        except Exception as e:
            return {'valid': False, 'errors': [f'配置验证过程出错: {str(e)}']}
    
    def _validate_network_config(self, network_config: Dict[str, Any]) -> List[str]:
        """验证网络配置"""
        errors = []
        
        # 验证type字段
        if 'type' not in network_config:
            errors.append("network_config缺少type字段")
            return errors
            
        config_type = network_config['type']
        if config_type not in ['predefined', 'custom']:
            errors.append(f"network_config.type无效值: {config_type}")
            
        # 验证条件字段
        if config_type == 'predefined':
            if network_config.get('file_path') is not None:
                errors.append("预设路网配置时file_path必须为null")
                
            if 'entrance_plan' not in network_config:
                errors.append("预设路网配置缺少entrance_plan字段")
            elif network_config['entrance_plan'] not in ['仅开放东侧出入口', '仅开放南侧出入口', '全部开放']:
                errors.append(f"无效的entrance_plan值: {network_config['entrance_plan']}")
                
        elif config_type == 'custom':
            if not network_config.get('file_path'):
                errors.append("自定义路网配置必须提供file_path")
            elif not network_config['file_path'].endswith('.net.xml'):
                errors.append("自定义路网文件必须是.net.xml格式")
            elif not os.path.exists(network_config['file_path']):
                errors.append(f"自定义路网文件不存在: {network_config['file_path']}")
        
        # 验证道路限行配置
        if 'road_restriction' in network_config:
            restriction = network_config['road_restriction']
            if 'enabled' not in restriction:
                errors.append("road_restriction缺少enabled字段")
            elif restriction.get('enabled'):
                # 检查vehicle_restricted_edges和pedestrian_restricted_edges
                vehicle_edges = restriction.get('vehicle_restricted_edges', [])
                pedestrian_edges = restriction.get('pedestrian_restricted_edges', [])
                
                # 启用限行时，至少需要一种类型的限行路段
                if not vehicle_edges and not pedestrian_edges:
                    errors.append("启用道路限行时必须提供车辆限行路段或行人限行路段")
                
                # 验证路段ID格式（基本检查）
                if vehicle_edges and not isinstance(vehicle_edges, list):
                    errors.append("vehicle_restricted_edges必须是数组格式")
                if pedestrian_edges and not isinstance(pedestrian_edges, list):
                    errors.append("pedestrian_restricted_edges必须是数组格式")
                
        return errors
    
    def _validate_signal_config(self, signal_config: Dict[str, Any]) -> List[str]:
        """验证信号配置"""
        errors = []
        
        # 验证type字段
        if 'type' not in signal_config:
            errors.append("signal_config缺少type字段")
            return errors
            
        config_type = signal_config['type']
        if config_type not in ['predefined', 'custom']:
            errors.append(f"signal_config.type无效值: {config_type}")
            
        # 验证条件字段
        if config_type == 'predefined':
            if signal_config.get('file_path') is not None:
                errors.append("预设信号配置时file_path必须为null")
        elif config_type == 'custom':
            if not signal_config.get('file_path'):
                errors.append("自定义信号配置必须提供file_path")
            elif not signal_config['file_path'].endswith('.add.xml'):
                errors.append("自定义信号文件必须是.add.xml格式")
            elif not os.path.exists(signal_config['file_path']):
                errors.append(f"自定义信号文件不存在: {signal_config['file_path']}")
        
        # 验证优化配置
        if 'optimization' in signal_config:
            optimization = signal_config['optimization']
            if 'enabled' not in optimization:
                errors.append("signal optimization缺少enabled字段")
            elif optimization.get('enabled') and not optimization.get('selected_intersections'):
                errors.append("启用信号优化时必须提供selected_intersections列表")
                
        return errors
    
    def _validate_traffic_config(self, traffic_config: Dict[str, Any]) -> List[str]:
        """验证交通配置"""
        errors = []
        
        # 验证type字段
        if 'type' not in traffic_config:
            errors.append("traffic_config缺少type字段")
            return errors
            
        config_type = traffic_config['type']
        if config_type not in ['predefined', 'custom']:
            errors.append(f"traffic_config.type无效值: {config_type}")
            
        # 验证条件字段
        if config_type == 'predefined':
            if traffic_config.get('file_path') is not None:
                errors.append("预设交通配置时file_path必须为null")
                
            if 'scenario' not in traffic_config:
                errors.append("预设交通配置缺少scenario字段")
            elif traffic_config['scenario'] not in ['进场', '离场']:
                errors.append(f"无效的scenario值: {traffic_config['scenario']}")
                
            if 'vehicle_type' not in traffic_config:
                errors.append("预设交通配置缺少vehicle_type字段")
            elif traffic_config['vehicle_type'] not in ['仅一般车辆', '存在贵宾专车']:
                errors.append(f"无效的vehicle_type值: {traffic_config['vehicle_type']}")
                
            if 'traffic_scale' not in traffic_config:
                errors.append("预设交通配置缺少traffic_scale字段")
            elif traffic_config['traffic_scale'] not in ['small', 'medium', 'large']:
                errors.append(f"无效的traffic_scale值: {traffic_config['traffic_scale']}")
                
        elif config_type == 'custom':
            if not traffic_config.get('file_path'):
                errors.append("自定义交通配置必须提供file_path")
            elif not traffic_config['file_path'].endswith('.rou.xml'):
                errors.append("自定义交通文件必须是.rou.xml格式")
            elif not os.path.exists(traffic_config['file_path']):
                errors.append(f"自定义交通文件不存在: {traffic_config['file_path']}")
        
        return errors
    
    def _validate_business_logic(self, config: Dict[str, Any]) -> List[str]:
        """验证业务逻辑约束"""
        errors = []
        
        traffic_config = config.get('traffic_config', {})
        
        # VIP优先通行约束
        if 'vip_priority' in traffic_config:
            vip_priority = traffic_config['vip_priority']
            vehicle_type = traffic_config.get('vehicle_type')
            
            if vip_priority.get('enabled') and vehicle_type != '存在贵宾专车':
                errors.append("VIP优先通行需要车辆类型为'存在贵宾专车'")
        
        return errors
    
    def get_network_file(self, config: Dict[str, Any]) -> str:
        """
        获取路网文件路径
        
        参数:
        config: dict - 完整配置
        
        返回:
        str - 路网文件路径
        """
        network_config = config['network_config']
        
        if network_config['type'] == 'custom':
            return network_config['file_path']
        else:
            return os.path.join(self.template_dir, 'gym_tls.net.xml')
    
    def get_signal_files(self, config: Dict[str, Any]) -> List[str]:
        """
        获取信号配时文件列表
        
        参数:
        config: dict - 完整配置
        
        返回:
        List[str] - 信号配时文件路径列表
        """
        signal_config = config['signal_config']
        
        if signal_config['type'] == 'custom':
            # 使用自定义信号文件
            return [signal_config['file_path']]
        else:
            return [os.path.join(self.template_dir, 'newtls.add.xml')]
    
    def get_route_file(self, config: Dict[str, Any]) -> str:
        """
        获取路由文件路径
        
        参数:
        config: dict - 完整配置
        
        返回:
        str - 路由文件路径（如果是预设配置则返回None，需要动态生成）
        """
        traffic_config = config['traffic_config']
        
        if traffic_config['type'] == 'custom':
            return traffic_config['file_path']
        else:
            # 预设交通需要动态生成
            return None
    
    def is_vip_scenario(self, config: Dict[str, Any]) -> bool:
        """
        判断是否为VIP场景
        
        参数:
        config: dict - 完整配置
        
        返回:
        bool - 是否为VIP场景
        """
        traffic_config = config['traffic_config']
        
        if traffic_config['type'] == 'predefined':
            return traffic_config.get('vehicle_type') == '存在贵宾专车'
        
        return False
    
    def is_vip_priority_enabled(self, config: Dict[str, Any]) -> bool:
        """
        判断是否启用VIP优先通行
        
        参数:
        config: dict - 完整配置
        
        返回:
        bool - 是否启用VIP优先通行
        """
        traffic_config = config['traffic_config']
        
        return (self.is_vip_scenario(config) and 
                traffic_config.get('vip_priority', {}).get('enabled', False))
    
    def get_scenario_type(self, config: Dict[str, Any]) -> str:
        """
        获取场景类型（进场/离场）
        
        参数:
        config: dict - 完整配置
        
        返回:
        str - 场景类型
        """
        traffic_config = config['traffic_config']
        
        if traffic_config['type'] == 'predefined':
            return traffic_config.get('scenario', '进场')
        
        return '进场'  # 自定义配置默认为进场
    
    def get_traffic_scale(self, config: Dict[str, Any]) -> float:
        """
        获取交通规模比例值
        
        参数:
        config: dict - 完整配置
        
        返回:
        float - 交通规模比例值（用于SUMO的--scale参数）
        """
        traffic_config = config['traffic_config']
        
        if traffic_config['type'] == 'predefined':
            scale_mapping = {
                'small': 0.75,
                'medium': 1.0, 
                'large': 1.25
            }
            scale_key = traffic_config.get('traffic_scale', 'medium')
            return scale_mapping.get(scale_key, 1.0)
        
        return 1.0  # 自定义配置默认为1.0
    
    def get_entrance_plan(self, config: Dict[str, Any]) -> str:
        """
        获取出入口方案
        
        参数:
        config: dict - 完整配置
        
        返回:
        str - 出入口方案
        """
        network_config = config['network_config']
        
        if network_config['type'] == 'predefined':
            return network_config.get('entrance_plan', '全部开放')
        
        return '全部开放'  # 自定义路网默认为全部开放
    
    def is_vehicle_restriction_enabled(self, config: Dict[str, Any]) -> bool:
        """
        判断是否启用车辆限行
        
        参数:
        config: dict - 完整配置
        
        返回:
        bool - 是否启用车辆限行
        """
        network_config = config['network_config']
        road_restriction = network_config.get('road_restriction', {})
        
        # 检查是否总体启用限行，且有车辆限行路段
        if road_restriction.get('enabled', False):
            vehicle_edges = road_restriction.get('vehicle_restricted_edges', [])
            return len(vehicle_edges) > 0
        
        return False
    
    def is_pedestrian_restriction_enabled(self, config: Dict[str, Any]) -> bool:
        """
        判断是否启用行人限行
        
        参数:
        config: dict - 完整配置
        
        返回:
        bool - 是否启用行人限行
        """
        network_config = config['network_config']
        road_restriction = network_config.get('road_restriction', {})
        
        # 检查是否总体启用限行，且有行人限行路段
        if road_restriction.get('enabled', False):
            pedestrian_edges = road_restriction.get('pedestrian_restricted_edges', [])
            return len(pedestrian_edges) > 0
        
        return False
    
    def get_vehicle_restricted_edges(self, config: Dict[str, Any]) -> List[str]:
        """
        获取车辆限行路段列表
        
        参数:
        config: dict - 完整配置
        
        返回:
        List[str] - 车辆限行路段ID列表
        """
        network_config = config['network_config']
        road_restriction = network_config.get('road_restriction', {})
        
        if road_restriction.get('enabled', False):
            return road_restriction.get('vehicle_restricted_edges', [])
        
        return []
    
    def get_pedestrian_restricted_edges(self, config: Dict[str, Any]) -> List[str]:
        """
        获取行人限行路段列表
        
        参数:
        config: dict - 完整配置
        
        返回:
        List[str] - 行人限行路段ID列表
        """
        network_config = config['network_config']
        road_restriction = network_config.get('road_restriction', {})
        
        if road_restriction.get('enabled', False):
            return road_restriction.get('pedestrian_restricted_edges', [])
        
        return []
    
    def has_any_restriction_enabled(self, config: Dict[str, Any]) -> bool:
        """
        检查是否有任何类型的限行启用
        
        参数:
        config: dict - 完整配置
        
        返回:
        bool - 是否有车辆或行人限行启用
        """
        network_config = config['network_config']
        road_restriction = network_config.get('road_restriction', {})
        
        if not road_restriction.get('enabled', False):
            return False
        
        # 检查是否有车辆或行人限行路段
        vehicle_edges = road_restriction.get('vehicle_restricted_edges', [])
        pedestrian_edges = road_restriction.get('pedestrian_restricted_edges', [])
        
        return len(vehicle_edges) > 0 or len(pedestrian_edges) > 0
    
    def get_optimization_intersections(self, config: Dict[str, Any]) -> List[str]:
        """
        获取需要优化的交叉口列表
        
        参数:
        config: dict - 完整配置
        
        返回:
        List[str] - 交叉口ID列表
        """
        signal_config = config['signal_config']
        
        optimization = signal_config.get('optimization', {})
        if optimization.get('enabled'):
            return optimization.get('selected_intersections', [])
        
        return []
    
    def is_signal_optimization_enabled(self, config: Dict[str, Any]) -> bool:
        """
        判断是否启用信号优化
        
        参数:
        config: dict - 完整配置
        
        返回:
        bool - 是否启用信号优化
        """
        signal_config = config['signal_config']
        
        return signal_config.get('optimization', {}).get('enabled', False)
    
    def generate_config_summary(self, config: Dict[str, Any]) -> Dict[str, str]:
        """
        生成配置摘要

        参数:
        config: dict - 配置数据

        返回:
        dict - 配置摘要
        """
        network_config = config['network_config']
        signal_config = config['signal_config']
        traffic_config = config['traffic_config']

        # 网络摘要
        if network_config['type'] == 'predefined':
            network_summary = f"预设路网 - {network_config.get('entrance_plan', '未指定')}"
            if network_config.get('road_restriction', {}).get('enabled'):
                # 正确统计车辆限行和行人限行路段数量
                vehicle_edges = network_config['road_restriction'].get('vehicle_restricted_edges', [])
                pedestrian_edges = network_config['road_restriction'].get('pedestrian_restricted_edges', [])
                total_restricted = len(vehicle_edges) + len(pedestrian_edges)

                # 获取路段名称
                edge_names = self._get_edge_names_from_ids(vehicle_edges + pedestrian_edges)
                if edge_names:
                    # 显示所有路段名称，不使用"等"省略
                    edge_names_str = "、".join(edge_names)
                    network_summary += f" + 道路限行({edge_names_str})"
                else:
                    network_summary += f" + 道路限行({total_restricted}条路段)"
        else:
            file_name = os.path.basename(network_config.get('file_path', '未知文件'))
            network_summary = f"自定义路网 - {file_name}"
            
        # 信号摘要
        if signal_config['type'] == 'predefined':
            signal_summary = "预设配时"
            signal_opt = signal_config.get('optimization', {})
            if signal_opt.get('enabled'):
                intersections = signal_opt.get('selected_intersections', [])
                intersection_count = len(intersections)

                # 获取交叉口名称
                intersection_names = self._get_intersection_names_from_ids(intersections)
                if intersection_names:
                    # 显示所有交叉口名称，不使用"等"省略
                    intersection_names_str = "、".join(intersection_names)
                    signal_summary += f" + 自定义优化({intersection_names_str})"
                else:
                    signal_summary += f" + 自定义优化({intersection_count}个交叉口)"
        else:
            file_name = os.path.basename(signal_config.get('file_path', '未知文件'))
            signal_summary = f"自定义配时 - {file_name}"
            
        # 交通摘要
        if traffic_config['type'] == 'predefined':
            scenario = traffic_config.get('scenario', '未指定')
            vehicle_type = traffic_config.get('vehicle_type', '未指定')
            traffic_scale = traffic_config.get('traffic_scale', 'medium')
            
            # 规模文本映射
            scale_text = {
                'small': '小规模',
                'medium': '中规模', 
                'large': '大规模'
            }
            
            traffic_summary = f"预设需求 - {scenario}场景 + {scale_text.get(traffic_scale, '中规模')}"
            if vehicle_type == '存在贵宾专车':
                traffic_summary += " + 贵宾专车"
                if traffic_config.get('vip_priority', {}).get('enabled'):
                    traffic_summary += "(优先通行)"
        else:
            file_name = os.path.basename(traffic_config.get('file_path', '未知文件'))
            traffic_summary = f"自定义需求 - {file_name}"
            
        return {
            'network': network_summary,
            'signal': signal_summary,
            'traffic': traffic_summary
        }

    def _get_edge_names_from_ids(self, edge_ids: List[str]) -> List[str]:
        """
        从路段ID获取路段名称

        参数:
        edge_ids: List[str] - 路段ID列表

        返回:
        List[str] - 路段名称列表
        """
        if not edge_ids:
            return []

        try:
            import xml.etree.ElementTree as ET

            # 获取路网文件路径
            net_file = os.path.join(self.template_dir, 'gym_tls.net.xml')
            if not os.path.exists(net_file):
                return []

            # 解析XML文件
            tree = ET.parse(net_file)
            root = tree.getroot()

            edge_names = []
            for edge_id in edge_ids:
                # 查找对应的edge元素
                edge_elem = root.find(f".//edge[@id='{edge_id}']")
                if edge_elem is not None:
                    name = edge_elem.get('name', '')
                    if name:
                        edge_names.append(name)
                    else:
                        edge_names.append(edge_id)  # 如果没有名称，使用ID
                else:
                    edge_names.append(edge_id)  # 如果找不到，使用ID

            return edge_names

        except Exception as e:
            print(f"获取路段名称失败: {e}")
            return edge_ids  # 出错时返回原始ID列表

    def _get_intersection_names_from_ids(self, intersection_ids: List[str]) -> List[str]:
        """
        从交叉口ID获取交叉口名称

        参数:
        intersection_ids: List[str] - 交叉口ID列表

        返回:
        List[str] - 交叉口名称列表
        """
        if not intersection_ids:
            return []

        try:
            import xml.etree.ElementTree as ET

            # 获取路网文件路径
            net_file = os.path.join(self.template_dir, 'gym_tls.net.xml')
            if not os.path.exists(net_file):
                return []

            # 解析XML文件
            tree = ET.parse(net_file)
            root = tree.getroot()

            intersection_names = []
            for intersection_id in intersection_ids:
                # 查找对应的junction元素
                junction_elem = root.find(f".//junction[@id='{intersection_id}']")
                if junction_elem is not None:
                    # 交叉口通常没有name属性，我们可以根据位置或连接的道路来生成名称
                    # 这里先简化处理，使用ID的简化版本
                    name = self._simplify_intersection_name(intersection_id)
                    intersection_names.append(name)
                else:
                    intersection_names.append(intersection_id)  # 如果找不到，使用ID

            return intersection_names

        except Exception as e:
            print(f"获取交叉口名称失败: {e}")
            return intersection_ids  # 出错时返回原始ID列表

    def _simplify_intersection_name(self, intersection_id: str) -> str:
        """
        简化交叉口名称显示

        参数:
        intersection_id: str - 交叉口ID

        返回:
        str - 简化的交叉口名称
        """
        # 如果是cluster类型的ID，提取主要部分
        if intersection_id.startswith('cluster_'):
            # 移除cluster_前缀，取第一个数字部分
            parts = intersection_id.replace('cluster_', '').split('_')
            if parts:
                return f"交叉口{parts[0][:8]}"  # 取前8位数字

        # 如果ID较短，直接使用
        if len(intersection_id) <= 12:
            return f"交叉口{intersection_id}"
        else:
            return f"交叉口{intersection_id[:8]}..."  # 截断长ID

    def enrich_config_with_names(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        为配置添加路段名称和交叉口名称信息

        参数:
        config: dict - 原始配置数据

        返回:
        dict - 增强后的配置数据（包含名称信息）
        """
        # 深拷贝配置以避免修改原始数据
        import copy
        enriched_config = copy.deepcopy(config)

        try:
            # 处理道路限行配置
            network_config = enriched_config.get('network_config', {})
            road_restriction = network_config.get('road_restriction', {})

            if road_restriction.get('enabled'):
                # 处理车辆限行路段
                vehicle_edges = road_restriction.get('vehicle_restricted_edges', [])
                if vehicle_edges:
                    vehicle_edge_names = self._get_edge_names_from_ids(vehicle_edges)
                    road_restriction['vehicle_restricted_edges_with_names'] = [
                        {
                            'id': edge_id,
                            'name': name
                        }
                        for edge_id, name in zip(vehicle_edges, vehicle_edge_names)
                    ]

                # 处理行人限行路段
                pedestrian_edges = road_restriction.get('pedestrian_restricted_edges', [])
                if pedestrian_edges:
                    pedestrian_edge_names = self._get_edge_names_from_ids(pedestrian_edges)
                    road_restriction['pedestrian_restricted_edges_with_names'] = [
                        {
                            'id': edge_id,
                            'name': name
                        }
                        for edge_id, name in zip(pedestrian_edges, pedestrian_edge_names)
                    ]

            # 处理信号优化配置
            signal_config = enriched_config.get('signal_config', {})
            optimization = signal_config.get('optimization', {})

            if optimization.get('enabled'):
                intersections = optimization.get('selected_intersections', [])
                if intersections:
                    intersection_names = self._get_intersection_names_from_ids(intersections)
                    optimization['selected_intersections_with_names'] = [
                        {
                            'id': intersection_id,
                            'name': name
                        }
                        for intersection_id, name in zip(intersections, intersection_names)
                    ]

            # 处理路段分析配置（如果存在）
            analysis_config = enriched_config.get('analysis_config', {})
            edge_analysis = analysis_config.get('edge_analysis', {})

            if edge_analysis.get('enabled'):
                selected_edges = edge_analysis.get('selected_edges', [])
                if selected_edges:
                    edge_names = self._get_edge_names_from_ids(selected_edges)
                    edge_analysis['selected_edges_with_names'] = [
                        {
                            'id': edge_id,
                            'name': name
                        }
                        for edge_id, name in zip(selected_edges, edge_names)
                    ]

            return enriched_config

        except Exception as e:
            print(f"增强配置时出错: {e}")
            return config  # 出错时返回原始配置