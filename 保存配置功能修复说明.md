# 保存配置功能修复说明

## 问题描述

用户要求检查保存当前配置按钮的功能，使其和 `backend\sumo\output\2023\activity` 目录的保存格式和内容相同。

## 问题分析

通过对比发现，原有的保存配置功能与目标目录中的文件格式存在以下差异：

### 原有格式（history目录）
```json
{
  "id": "250801_171412",
  "name": "方案名称",
  "description": "方案描述",
  "created_time": "2025-08-01T17:14:12.291025",
  "config": {...},
  "config_summary": {...},
  "simulation_results": {...}
}
```

### 目标格式（activity目录）
```json
{
  "simulation_id": "250801171330",
  "config": {...},
  "config_summary": {...},
  "start_time": "2025-08-01T17:13:54.079206",
  "simulation_results": {...}
}
```

## 主要差异

1. **字段名称不一致**：
   - `id` vs `simulation_id`
   - `created_time` vs `start_time`

2. **字段结构不同**：
   - 原有格式包含 `name` 和 `description` 字段
   - 目标格式更简洁，只包含核心数据

3. **缩进格式不同**：
   - 原有格式使用2个空格缩进
   - 目标格式使用4个空格缩进

## 解决方案

修改了 `backend\history_manager.py` 中的 `save_scheme` 方法，实现以下功能：

### 1. 双格式保存
- **History目录**：保持原有格式以确保兼容性
- **Activity目录**：使用新格式与目标目录一致

### 2. 格式统一
- 使用 `simulation_id` 替代 `id`
- 使用 `start_time` 替代 `created_time`
- 使用4个空格缩进

### 3. 自动保存到目标目录
- 每次保存配置时，自动在 `backend\sumo\output\2023\activity` 目录创建对应文件
- 文件名格式：`simulation_result_{scheme_id}.json`

## 修改的代码文件

### backend\history_manager.py
- 修改了 `save_scheme` 方法
- 添加了双格式保存逻辑
- 添加了自动保存到activity目录的功能

## 验证结果

通过多项测试验证，确认修复后的功能完全符合要求：

### ✅ 格式验证
- 顶级字段结构完全一致
- 所有必要字段都存在
- config字段结构一致
- simulation_results字段结构一致

### ✅ 文件保存验证
- Activity目录文件正确创建
- History目录文件保持兼容性
- 使用4个空格缩进（与activity目录一致）

### ✅ 功能验证
- 前端保存按钮正常工作
- API接口响应正确
- 文件内容格式正确

## 使用方法

1. **前端使用**：
   - 点击"保存当前配置"按钮
   - 填写方案名称和描述（可选）
   - 系统自动保存到两个位置

2. **API使用**：
   ```javascript
   fetch('/api/history/save', {
     method: 'POST',
     headers: {'Content-Type': 'application/json'},
     body: JSON.stringify({
       config: configData,
       name: "方案名称",
       description: "方案描述",
       simulation_results: resultsData
     })
   })
   ```

## 保存位置

每次保存配置时，会在以下两个位置创建文件：

1. **History目录**：`backend\sumo_data\history\scheme_{scheme_id}.json`
   - 用于历史方案管理
   - 保持原有格式以确保兼容性

2. **Activity目录**：`backend\sumo\output\2023\activity\simulation_result_{scheme_id}.json`
   - 与仿真结果文件格式完全一致
   - 满足项目方要求的格式标准

## 重要更新：完整simulation_results结构保证

### 新增功能
为了确保所有保存的配置都包含完整的 `simulation_results` 结构，新增了以下功能：

1. **默认simulation_results结构**：
   - 当用户保存配置时未提供 `simulation_results` 时，系统自动添加完整的默认结构
   - 默认结构与标准格式完全一致，所有数值初始化为0或0.0

2. **完整结构验证**：
   - 确保所有保存的文件都包含完整的 `simulation_results` 结构
   - 包含所有必要的子字段：`network_metrics`、`selected_edge_metrics`
   - 每个子字段都包含完整的指标数据

### 标准simulation_results结构
```json
{
  "simulation_results": {
    "network_metrics": {
      "pedestrian_metrics": {
        "average_travel_time": 0.0,
        "average_waiting_time": 0.0,
        "average_waiting_count": 0.0,
        "average_time_loss": 0.0
      },
      "vehicle_metrics": {
        "average_travel_time": 0.0,
        "average_waiting_time": 0.0,
        "average_waiting_count": 0.0,
        "average_time_loss": 0.0
      },
      "vip_vehicle_metrics": {
        "average_travel_time": 0,
        "average_waiting_time": 0,
        "average_waiting_count": 0,
        "average_time_loss": 0
      },
      "venue_area_metrics": {
        "average_pedestrian_travel_time": 0,
        "average_pedestrian_delay": 0,
        "average_vehicle_travel_time": 0,
        "average_vehicle_delay": 0
      }
    },
    "selected_edge_metrics": {
      "pedestrian_metrics": {
        "total_departed": 0.0,
        "total_arrived": 0.0,
        "total_entered": 0.0,
        "avg_traveltime": 0,
        "avg_waitingTime": 0,
        "avg_speed": 0.0,
        "avg_timeLoss": 0,
        "avg_occupancy": 0.0
      },
      "vehicle_metrics": {
        "total_departed": 0,
        "total_arrived": 0.0,
        "total_entered": 0.0,
        "avg_traveltime": 0.0,
        "avg_waitingTime": 0.0,
        "avg_speed": 0.0,
        "avg_timeLoss": 0.0,
        "avg_occupancy": 0.0
      }
    }
  }
}
```

## 总结

通过本次修复，保存当前配置按钮的功能现在完全符合要求：
- ✅ 保存格式与 `backend\sumo\output\2023\activity` 目录一致
- ✅ 保存内容结构完全匹配
- ✅ **所有保存的配置都包含完整的simulation_results结构**
- ✅ 支持自定义simulation_results和默认结构
- ✅ 保持向后兼容性
- ✅ 前端功能正常工作
- ✅ API接口稳定可靠

修复后的功能既满足了新的格式要求，又保持了系统的稳定性和兼容性。最重要的是，现在所有保存的配置文件都包含完整的 `simulation_results` 结构，与您指定的标准格式完全一致。
