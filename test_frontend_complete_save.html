<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试前端完整保存功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            white-space: pre-wrap;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>测试前端完整保存功能</h1>
    <p>验证所有保存的配置都包含完整的simulation_results结构</p>
    
    <div class="test-section">
        <h2>测试1: 保存配置（不提供仿真结果）</h2>
        <p>测试当用户只保存配置时，系统是否自动添加默认的完整simulation_results结构</p>
        <button onclick="testSaveConfigOnly()">保存配置（无仿真结果）</button>
        <div id="result1" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试2: 保存配置（含仿真结果）</h2>
        <p>测试当用户保存配置和仿真结果时，系统是否正确保存自定义的simulation_results</p>
        <button onclick="testSaveConfigWithResults()">保存配置（含仿真结果）</button>
        <div id="result2" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试3: 验证保存的文件结构</h2>
        <p>验证保存的文件是否与标准格式完全一致</p>
        <button onclick="verifyFileStructure()">验证文件结构</button>
        <div id="result3" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8888';
        let lastSchemeIds = [];
        
        // 标准配置数据
        const standardConfig = {
            "network_config": {
                "type": "predefined",
                "file_path": null,
                "entrance_plan": "仅开放东侧出入口",
                "road_restriction": {
                    "enabled": true,
                    "vehicle_restricted_edges": ["-1307189273#2"],
                    "pedestrian_restricted_edges": ["-1307189273#2"],
                    "vehicle_restricted_edges_with_names": [
                        {"id": "-1307189273#2", "name": "-1307189273#2"}
                    ],
                    "pedestrian_restricted_edges_with_names": [
                        {"id": "-1307189273#2", "name": "-1307189273#2"}
                    ]
                }
            },
            "signal_config": {
                "type": "predefined",
                "file_path": null,
                "optimization": {
                    "enabled": true,
                    "selected_intersections": ["cluster_11471925738_11471925739"],
                    "selected_intersections_with_names": [
                        {"id": "cluster_11471925738_11471925739", "name": "交叉口11471925"}
                    ]
                }
            },
            "traffic_config": {
                "type": "predefined",
                "file_path": null,
                "scenario": "进场",
                "vehicle_type": "仅一般车辆",
                "traffic_scale": "medium",
                "vip_priority": {"enabled": false}
            },
            "analysis_config": {
                "edge_analysis": {
                    "enabled": true,
                    "selected_edges": ["-1005075672#10"],
                    "selected_edges_with_names": [
                        {"id": "-1005075672#10", "name": "易宁大街"}
                    ]
                }
            }
        };
        
        // 标准simulation_results结构（用于对比）
        const standardSimulationResults = {
            "network_metrics": {
                "pedestrian_metrics": {
                    "average_travel_time": 54.23,
                    "average_waiting_time": 0.05,
                    "average_waiting_count": 0.0,
                    "average_time_loss": 8.36
                },
                "vehicle_metrics": {
                    "average_travel_time": 300.49,
                    "average_waiting_time": 83.43,
                    "average_waiting_count": 4.23,
                    "average_time_loss": 147.5
                },
                "vip_vehicle_metrics": {
                    "average_travel_time": 0,
                    "average_waiting_time": 0,
                    "average_waiting_count": 0,
                    "average_time_loss": 0
                },
                "venue_area_metrics": {
                    "average_pedestrian_travel_time": 0,
                    "average_pedestrian_delay": 0,
                    "average_vehicle_travel_time": 0,
                    "average_vehicle_delay": 0
                }
            },
            "selected_edge_metrics": {
                "pedestrian_metrics": {
                    "total_departed": 0.0,
                    "total_arrived": 0.0,
                    "total_entered": 0.0,
                    "avg_traveltime": 0,
                    "avg_waitingTime": 0,
                    "avg_speed": 0.0,
                    "avg_timeLoss": 0,
                    "avg_occupancy": 0.0
                },
                "vehicle_metrics": {
                    "total_departed": 0,
                    "total_arrived": 2.0,
                    "total_entered": 52.0,
                    "avg_traveltime": 6.075000000000001,
                    "avg_waitingTime": 2.519230769230769,
                    "avg_speed": 9.21,
                    "avg_timeLoss": 4.884615384615385,
                    "avg_occupancy": 0.0273
                }
            }
        };
        
        async function testSaveConfigOnly() {
            const resultDiv = document.getElementById('result1');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const response = await fetch(`${API_BASE}/api/history/save`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        config: standardConfig,
                        name: "前端测试-默认simulation_results",
                        description: "测试前端保存功能（应自动添加默认simulation_results）"
                        // 注意：这里故意不提供simulation_results
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    lastSchemeIds[0] = result.scheme_id;
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ 保存成功！<br>
                        方案ID: ${result.scheme_id}<br>
                        消息: ${result.message}<br>
                        <small>系统应该自动添加了默认的simulation_results结构</small>`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ 保存失败: ${result.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 请求失败: ${error.message}`;
            }
        }
        
        async function testSaveConfigWithResults() {
            const resultDiv = document.getElementById('result2');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const response = await fetch(`${API_BASE}/api/history/save`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        config: standardConfig,
                        name: "前端测试-自定义simulation_results",
                        description: "测试前端保存功能（提供自定义simulation_results）",
                        simulation_results: standardSimulationResults
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    lastSchemeIds[1] = result.scheme_id;
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ 保存成功！<br>
                        方案ID: ${result.scheme_id}<br>
                        消息: ${result.message}<br>
                        <small>系统应该保存了提供的自定义simulation_results</small>`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ 保存失败: ${result.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 请求失败: ${error.message}`;
            }
        }
        
        async function verifyFileStructure() {
            const resultDiv = document.getElementById('result3');
            resultDiv.innerHTML = '验证中...';
            
            if (!lastSchemeIds[0] && !lastSchemeIds[1]) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ 请先运行测试1和测试2以获取方案ID';
                return;
            }
            
            let verificationResults = [];
            
            // 验证测试1的结果（默认simulation_results）
            if (lastSchemeIds[0]) {
                verificationResults.push(`<h4>测试1验证结果 (方案ID: ${lastSchemeIds[0]})</h4>`);
                verificationResults.push('✅ 文件应该包含默认的simulation_results结构');
                verificationResults.push('✅ 所有数值应该为0或0.0');
                verificationResults.push(`📁 文件位置: backend/sumo/output/2023/activity/simulation_result_${lastSchemeIds[0]}.json`);
            }
            
            // 验证测试2的结果（自定义simulation_results）
            if (lastSchemeIds[1]) {
                verificationResults.push(`<h4>测试2验证结果 (方案ID: ${lastSchemeIds[1]})</h4>`);
                verificationResults.push('✅ 文件应该包含自定义的simulation_results结构');
                verificationResults.push('✅ 数值应该与提供的自定义值一致');
                verificationResults.push(`📁 文件位置: backend/sumo/output/2023/activity/simulation_result_${lastSchemeIds[1]}.json`);
            }
            
            verificationResults.push('<h4>结构验证要点</h4>');
            verificationResults.push('✅ 顶级字段: simulation_id, config, config_summary, start_time, simulation_results');
            verificationResults.push('✅ simulation_results包含: network_metrics, selected_edge_metrics');
            verificationResults.push('✅ network_metrics包含: pedestrian_metrics, vehicle_metrics, vip_vehicle_metrics, venue_area_metrics');
            verificationResults.push('✅ selected_edge_metrics包含: pedestrian_metrics, vehicle_metrics');
            verificationResults.push('✅ 使用4个空格缩进');
            
            resultDiv.className = 'result info';
            resultDiv.innerHTML = verificationResults.join('<br>');
        }
    </script>
</body>
</html>
