#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试通过API保存的功能
"""

import requests
import json

def test_api_save():
    """测试通过API保存的功能"""
    print("=== 测试API保存功能 ===\n")
    
    # API基础URL
    api_base = "http://localhost:5000"
    
    # 测试配置数据
    test_config = {
        "network_config": {
            "type": "predefined",
            "entrance_plan": "仅开放东侧出入口",
            "road_restriction": {
                "enabled": True,
                "vehicle_restricted_edges": ["edge1"],
                "pedestrian_restricted_edges": ["edge2"]
            }
        },
        "signal_config": {
            "type": "predefined",
            "optimization": {
                "enabled": True,
                "selected_intersections": ["int1"]
            }
        },
        "traffic_config": {
            "type": "predefined",
            "scenario": "进场",
            "vehicle_type": "仅一般车辆",
            "traffic_scale": "medium",
            "vip_priority": {
                "enabled": False
            }
        }
    }
    
    # 部分仿真结果
    partial_simulation_results = {
        "network_metrics": {
            "pedestrian_metrics": {
                "average_travel_time": 95.5,
                "average_waiting_time": 15.2,
                "average_waiting_count": 1.5,
                "average_time_loss": 12.8
            },
            "vehicle_metrics": {
                "average_travel_time": 220.3,
                "average_waiting_time": 65.7,
                "average_waiting_count": 4.2,
                "average_time_loss": 55.1
            }
            # 故意缺少其他指标
        }
    }
    
    # 准备请求数据
    request_data = {
        "config": test_config,
        "name": "API测试方案",
        "description": "通过API保存的测试方案",
        "simulation_results": partial_simulation_results
    }
    
    print("发送保存请求...")
    print(f"URL: {api_base}/api/history/save")
    print(f"数据: {json.dumps(request_data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            f"{api_base}/api/history/save",
            headers={'Content-Type': 'application/json'},
            json=request_data,
            timeout=30
        )
        
        print(f"\n响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API保存成功")
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                print(f"方案ID: {result.get('scheme_id', 'N/A')}")
                print(f"消息: {result.get('message', 'N/A')}")
            else:
                print(f"❌ 保存失败: {result.get('error', '未知错误')}")
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器")
        print("请确保API服务器正在运行 (python backend/api_server.py)")
    except Exception as e:
        print(f"❌ 请求过程中出错: {str(e)}")

if __name__ == "__main__":
    test_api_save()
