#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前端保存功能的完整性
"""

import requests
import json
import time

def test_frontend_save_completeness():
    """测试前端保存功能的完整性"""
    print("=== 测试前端保存功能完整性 ===\n")
    
    # API基础URL
    api_base = "http://localhost:5000"
    
    # 模拟前端buildConfigJSON函数构建的配置
    frontend_config = {
        "network_config": {
            "type": "predefined",
            "file_path": None,
            "entrance_plan": "仅开放东侧出入口",
            "road_restriction": {
                "enabled": True,
                "vehicle_restricted_edges": ["edge1", "edge2"],
                "pedestrian_restricted_edges": ["edge3"]
            }
        },
        "signal_config": {
            "type": "predefined", 
            "file_path": None,
            "optimization": {
                "enabled": True,
                "selected_intersections": ["int1", "int2"]
            }
        },
        "traffic_config": {
            "type": "predefined",
            "file_path": None,
            "scenario": "进场",
            "vehicle_type": "仅一般车辆",
            "traffic_scale": "medium",
            "vip_priority": {
                "enabled": False
            }
        },
        "analysis_config": {
            "edge_analysis": {
                "enabled": True,
                "selected_edges": ["edge4", "edge5"]
            }
        }
    }
    
    # 模拟前端可能获取到的仿真结果（来自window.currentLoadedResultData）
    frontend_simulation_results = {
        "network_metrics": {
            "pedestrian_metrics": {
                "average_travel_time": 85.5,
                "average_waiting_time": 12.3,
                "average_waiting_count": 1.8,
                "average_time_loss": 15.2
            },
            "vehicle_metrics": {
                "average_travel_time": 195.7,
                "average_waiting_time": 58.4,
                "average_waiting_count": 3.9,
                "average_time_loss": 72.1
            },
            "vip_vehicle_metrics": {
                "average_travel_time": 0,
                "average_waiting_time": 0,
                "average_waiting_count": 0,
                "average_time_loss": 0
            },
            "venue_area_metrics": {
                "average_pedestrian_travel_time": 0,
                "average_pedestrian_delay": 0,
                "average_vehicle_travel_time": 0,
                "average_vehicle_delay": 0
            }
        },
        "selected_edge_metrics": {
            "pedestrian_metrics": {
                "total_departed": 0.0,
                "total_arrived": 0.0,
                "total_entered": 0.0,
                "avg_traveltime": 0,
                "avg_waitingTime": 0,
                "avg_speed": 0.0,
                "avg_timeLoss": 0,
                "avg_occupancy": 0.0
            },
            "vehicle_metrics": {
                "total_departed": 0,
                "total_arrived": 15.0,
                "total_entered": 45.0,
                "avg_traveltime": 8.5,
                "avg_waitingTime": 3.2,
                "avg_speed": 12.5,
                "avg_timeLoss": 6.8,
                "avg_occupancy": 0.035
            }
        }
    }
    
    print("1. 测试前端完整配置和结果保存...")
    
    # 模拟前端saveCurrentScheme函数的请求
    request_data = {
        "config": frontend_config,
        "name": "前端测试方案-完整",
        "description": "测试前端保存完整配置和仿真结果",
        "simulation_results": frontend_simulation_results
    }
    
    try:
        response = requests.post(
            f"{api_base}/api/history/save",
            headers={'Content-Type': 'application/json'},
            json=request_data,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                scheme_id = result.get('scheme_id')
                print(f"✅ 完整保存成功，方案ID: {scheme_id}")
                
                # 验证保存的文件
                verify_saved_files(scheme_id, "完整配置和结果")
            else:
                print(f"❌ 保存失败: {result.get('error')}")
        else:
            print(f"❌ API请求失败: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器，请确保服务器正在运行")
        return
    except Exception as e:
        print(f"❌ 请求过程中出错: {str(e)}")
        return
    
    print("\n" + "="*50)
    
    print("2. 测试前端仅配置保存（无仿真结果）...")
    
    # 模拟前端没有仿真结果时的保存
    request_data_no_results = {
        "config": frontend_config,
        "name": "前端测试方案-仅配置",
        "description": "测试前端保存仅配置信息",
        "simulation_results": None
    }
    
    try:
        response = requests.post(
            f"{api_base}/api/history/save",
            headers={'Content-Type': 'application/json'},
            json=request_data_no_results,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                scheme_id = result.get('scheme_id')
                print(f"✅ 仅配置保存成功，方案ID: {scheme_id}")
                
                # 验证保存的文件
                verify_saved_files(scheme_id, "仅配置")
            else:
                print(f"❌ 保存失败: {result.get('error')}")
        else:
            print(f"❌ API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求过程中出错: {str(e)}")

def verify_saved_files(scheme_id, test_type):
    """验证保存的文件内容"""
    import os
    
    print(f"\n--- 验证{test_type}保存文件 ---")
    
    # 检查历史文件
    history_file = f"sumo_data/history/scheme_{scheme_id}.json"
    activity_file = f"backend/sumo/output/2023/activity/simulation_result_{scheme_id}.json"
    
    for file_path, file_type in [(history_file, "历史文件"), (activity_file, "项目方文件")]:
        if os.path.exists(file_path):
            print(f"✅ {file_type}存在: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 检查必要字段
            required_fields = ['simulation_id', 'config', 'config_summary', 'start_time', 'simulation_results']
            for field in required_fields:
                if field in data:
                    print(f"  ✅ {field}: 存在")
                else:
                    print(f"  ❌ {field}: 缺失")
            
            # 检查仿真结果结构
            if 'simulation_results' in data:
                sim_results = data['simulation_results']
                
                # 检查network_metrics
                if 'network_metrics' in sim_results:
                    network_metrics = sim_results['network_metrics']
                    required_metrics = ['pedestrian_metrics', 'vehicle_metrics', 'vip_vehicle_metrics', 'venue_area_metrics']
                    for metric in required_metrics:
                        if metric in network_metrics:
                            print(f"  ✅ network_metrics.{metric}: 存在")
                        else:
                            print(f"  ❌ network_metrics.{metric}: 缺失")
                
                # 检查selected_edge_metrics
                if 'selected_edge_metrics' in sim_results:
                    print(f"  ✅ selected_edge_metrics: 存在")
                else:
                    print(f"  ❌ selected_edge_metrics: 缺失")
        else:
            print(f"❌ {file_type}不存在: {file_path}")

if __name__ == "__main__":
    test_frontend_save_completeness()
