#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试优先级问题
"""

import os
import sys
import json

# 添加backend目录到路径
sys.path.append('backend')

from history_manager import HistoryManager

def debug_priority():
    """调试优先级问题"""
    print("=== 调试优先级问题 ===\n")
    
    # 创建历史管理器
    history_manager = HistoryManager()
    
    # 检查特定ID的数据
    target_id = "250804_112035"
    
    print(f"1. 检查ID {target_id} 在历史文件中的数据:")
    history_data = history_manager._load_history_data()
    history_scheme = None
    for scheme in history_data:
        if scheme['id'] == target_id:
            history_scheme = scheme
            break
    
    if history_scheme:
        print(f"✅ 在历史文件中找到:")
        print(f"   名称: {history_scheme['name']}")
        print(f"   描述: {history_scheme['description']}")
        print(f"   时间: {history_scheme['created_time']}")
    else:
        print(f"❌ 在历史文件中未找到")
    
    print(f"\n2. 检查ID {target_id} 在仿真结果目录中的数据:")
    simulation_data = history_manager._load_simulation_results_data()
    simulation_scheme = None
    for scheme in simulation_data:
        if scheme['id'] == target_id:
            simulation_scheme = scheme
            break
    
    if simulation_scheme:
        print(f"✅ 在仿真结果目录中找到:")
        print(f"   名称: {simulation_scheme['name']}")
        print(f"   描述: {simulation_scheme['description']}")
        print(f"   时间: {simulation_scheme['created_time']}")
    else:
        print(f"❌ 在仿真结果目录中未找到")
    
    print(f"\n3. 检查合并后的方案列表中的数据:")
    schemes = history_manager.get_schemes_list()
    merged_scheme = None
    for scheme in schemes:
        if scheme['id'] == target_id:
            merged_scheme = scheme
            break
    
    if merged_scheme:
        print(f"✅ 在合并列表中找到:")
        print(f"   名称: {merged_scheme['name']}")
        print(f"   描述: {merged_scheme['description']}")
        print(f"   时间: {merged_scheme['created_time']}")
        print(f"   来源: {merged_scheme['source']}")
    else:
        print(f"❌ 在合并列表中未找到")
    
    print(f"\n4. 分析优先级问题:")
    if history_scheme and simulation_scheme and merged_scheme:
        if merged_scheme['name'] == history_scheme['name']:
            print("✅ 优先级正确：使用了历史文件中的数据")
        elif merged_scheme['name'] == simulation_scheme['name']:
            print("❌ 优先级错误：使用了仿真结果目录中的数据")
            print("   应该优先使用历史文件中的数据")
        else:
            print("⚠️ 数据来源不明")
    
    print(f"\n5. 检查去重逻辑的执行顺序:")
    print("历史文件加载顺序:")
    for i, scheme in enumerate(history_data[:5], 1):
        print(f"   {i}. {scheme['id']}: {scheme['name']}")
    
    print("仿真结果加载顺序:")
    for i, scheme in enumerate(simulation_data[:5], 1):
        print(f"   {i}. {scheme['id']}: {scheme['name']}")

if __name__ == "__main__":
    debug_priority()
