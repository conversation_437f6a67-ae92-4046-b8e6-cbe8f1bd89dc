#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比较两个方案的差异
"""

import os
import sys
import json

# 添加backend目录到路径
sys.path.append('backend')

from history_manager import HistoryManager

def compare_schemes():
    """比较两个方案的差异"""
    print("=== 比较方案差异 ===\n")
    
    # 创建历史管理器
    history_manager = HistoryManager()
    
    # 要比较的两个方案ID
    scheme1_id = "250804_112035"  # 方案1 (错误的)
    scheme2_id = "250804_114535"  # 方案2 (正确的)
    
    # 获取方案详情
    scheme1 = history_manager.get_scheme_by_id(scheme1_id)
    scheme2 = history_manager.get_scheme_by_id(scheme2_id)
    
    if not scheme1:
        print(f"❌ 找不到方案1: {scheme1_id}")
        return
    
    if not scheme2:
        print(f"❌ 找不到方案2: {scheme2_id}")
        return
    
    print(f"方案1 ({scheme1_id}): {scheme1['name']}")
    print(f"方案2 ({scheme2_id}): {scheme2['name']}")
    print()
    
    # 比较基本信息
    print("=== 基本信息比较 ===")
    print(f"方案1 名称: {scheme1.get('name', 'N/A')}")
    print(f"方案2 名称: {scheme2.get('name', 'N/A')}")
    print(f"方案1 描述: {scheme1.get('description', 'N/A')}")
    print(f"方案2 描述: {scheme2.get('description', 'N/A')}")
    print(f"方案1 时间: {scheme1.get('created_time', 'N/A')}")
    print(f"方案2 时间: {scheme2.get('created_time', 'N/A')}")
    print()
    
    # 比较配置
    print("=== 配置比较 ===")
    config1 = scheme1.get('config', {})
    config2 = scheme2.get('config', {})
    
    # 网络配置
    print("网络配置:")
    net1 = config1.get('network_config', {})
    net2 = config2.get('network_config', {})
    
    print(f"  方案1 类型: {net1.get('type', 'N/A')}")
    print(f"  方案2 类型: {net2.get('type', 'N/A')}")
    print(f"  方案1 出入口: {net1.get('entrance_plan', 'N/A')}")
    print(f"  方案2 出入口: {net2.get('entrance_plan', 'N/A')}")
    
    # 道路限行
    restriction1 = net1.get('road_restriction', {})
    restriction2 = net2.get('road_restriction', {})
    print(f"  方案1 道路限行: {restriction1.get('enabled', False)}")
    print(f"  方案2 道路限行: {restriction2.get('enabled', False)}")
    
    if restriction1.get('enabled'):
        vehicle_edges1 = restriction1.get('vehicle_restricted_edges', [])
        pedestrian_edges1 = restriction1.get('pedestrian_restricted_edges', [])
        print(f"    方案1 车辆限行路段: {len(vehicle_edges1)} 个")
        print(f"    方案1 行人限行路段: {len(pedestrian_edges1)} 个")
        if vehicle_edges1:
            print(f"      车辆限行: {vehicle_edges1}")
        if pedestrian_edges1:
            print(f"      行人限行: {pedestrian_edges1}")
    
    if restriction2.get('enabled'):
        vehicle_edges2 = restriction2.get('vehicle_restricted_edges', [])
        pedestrian_edges2 = restriction2.get('pedestrian_restricted_edges', [])
        print(f"    方案2 车辆限行路段: {len(vehicle_edges2)} 个")
        print(f"    方案2 行人限行路段: {len(pedestrian_edges2)} 个")
        if vehicle_edges2:
            print(f"      车辆限行: {vehicle_edges2}")
        if pedestrian_edges2:
            print(f"      行人限行: {pedestrian_edges2}")
    
    print()
    
    # 信号配置
    print("信号配置:")
    signal1 = config1.get('signal_config', {})
    signal2 = config2.get('signal_config', {})
    
    print(f"  方案1 类型: {signal1.get('type', 'N/A')}")
    print(f"  方案2 类型: {signal2.get('type', 'N/A')}")
    
    opt1 = signal1.get('optimization', {})
    opt2 = signal2.get('optimization', {})
    print(f"  方案1 优化: {opt1.get('enabled', False)}")
    print(f"  方案2 优化: {opt2.get('enabled', False)}")
    
    if opt1.get('enabled'):
        intersections1 = opt1.get('selected_intersections', [])
        print(f"    方案1 优化交叉口: {len(intersections1)} 个")
        if intersections1:
            print(f"      交叉口: {intersections1}")
    
    if opt2.get('enabled'):
        intersections2 = opt2.get('selected_intersections', [])
        print(f"    方案2 优化交叉口: {len(intersections2)} 个")
        if intersections2:
            print(f"      交叉口: {intersections2}")
    
    print()
    
    # 交通配置
    print("交通配置:")
    traffic1 = config1.get('traffic_config', {})
    traffic2 = config2.get('traffic_config', {})
    
    print(f"  方案1 类型: {traffic1.get('type', 'N/A')}")
    print(f"  方案2 类型: {traffic2.get('type', 'N/A')}")
    print(f"  方案1 场景: {traffic1.get('scenario', 'N/A')}")
    print(f"  方案2 场景: {traffic2.get('scenario', 'N/A')}")
    print(f"  方案1 车辆类型: {traffic1.get('vehicle_type', 'N/A')}")
    print(f"  方案2 车辆类型: {traffic2.get('vehicle_type', 'N/A')}")
    
    vip1 = traffic1.get('vip_priority', {})
    vip2 = traffic2.get('vip_priority', {})
    print(f"  方案1 VIP优先: {vip1.get('enabled', False)}")
    print(f"  方案2 VIP优先: {vip2.get('enabled', False)}")
    
    print()
    
    # 分析配置
    print("分析配置:")
    analysis1 = config1.get('analysis_config', {})
    analysis2 = config2.get('analysis_config', {})
    
    edge1 = analysis1.get('edge_analysis', {})
    edge2 = analysis2.get('edge_analysis', {})
    print(f"  方案1 路段分析: {edge1.get('enabled', False)}")
    print(f"  方案2 路段分析: {edge2.get('enabled', False)}")
    
    if edge1.get('enabled'):
        edges1 = edge1.get('selected_edges', [])
        print(f"    方案1 分析路段: {len(edges1)} 个")
        if edges1:
            print(f"      路段: {edges1}")
    
    if edge2.get('enabled'):
        edges2 = edge2.get('selected_edges', [])
        print(f"    方案2 分析路段: {len(edges2)} 个")
        if edges2:
            print(f"      路段: {edges2}")
    
    print()
    
    # 比较仿真结果
    print("=== 仿真结果比较 ===")
    results1 = scheme1.get('simulation_results', {})
    results2 = scheme2.get('simulation_results', {})
    
    if results1:
        print("方案1 有仿真结果")
    else:
        print("方案1 无仿真结果")
    
    if results2:
        print("方案2 有仿真结果")
    else:
        print("方案2 无仿真结果")

if __name__ == "__main__":
    compare_schemes()
