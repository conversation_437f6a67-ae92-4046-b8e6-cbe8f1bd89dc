#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试部分仿真结果的合并功能
"""

import os
import sys
import json

# 添加backend目录到路径
sys.path.append('backend')

from history_manager import HistoryManager

def test_partial_merge():
    """测试部分仿真结果的合并功能"""
    print("=== 测试部分仿真结果合并 ===\n")
    
    # 创建历史管理器
    history_manager = HistoryManager()
    
    # 简单的测试配置
    test_config = {
        "network_config": {
            "type": "predefined",
            "entrance_plan": "仅开放东侧出入口"
        },
        "signal_config": {
            "type": "predefined"
        },
        "traffic_config": {
            "type": "predefined",
            "scenario": "进场"
        }
    }
    
    # 部分仿真结果（只有部分指标）
    partial_results = {
        "network_metrics": {
            "pedestrian_metrics": {
                "average_travel_time": 120.5,
                "average_waiting_time": 30.2
                # 故意缺少 average_waiting_count 和 average_time_loss
            },
            "vehicle_metrics": {
                "average_travel_time": 180.3,
                "average_waiting_time": 45.7,
                "average_waiting_count": 3.2,
                "average_time_loss": 40.1
            }
            # 故意缺少 vip_vehicle_metrics 和 venue_area_metrics
        }
        # 故意缺少 selected_edge_metrics
    }
    
    print("输入的部分仿真结果:")
    print(json.dumps(partial_results, indent=2, ensure_ascii=False))
    
    print("\n开始保存...")
    
    result = history_manager.save_scheme(
        config=test_config,
        name="部分结果合并测试",
        description="测试部分仿真结果的自动补全",
        simulation_results=partial_results
    )
    
    if result['success']:
        scheme_id = result['scheme_id']
        print(f"✅ 保存成功，方案ID: {scheme_id}")
        
        # 检查保存的文件
        scheme_file = result['scheme_file']
        with open(scheme_file, 'r', encoding='utf-8') as f:
            saved_data = json.load(f)
        
        print("\n保存后的完整仿真结果:")
        print(json.dumps(saved_data['simulation_results'], indent=2, ensure_ascii=False))
        
        # 验证合并结果
        sim_results = saved_data['simulation_results']
        
        print("\n=== 验证合并结果 ===")
        
        # 检查输入的数据是否保留
        if (sim_results['network_metrics']['pedestrian_metrics']['average_travel_time'] == 120.5 and
            sim_results['network_metrics']['vehicle_metrics']['average_travel_time'] == 180.3):
            print("✅ 输入的数据正确保留")
        else:
            print("❌ 输入的数据丢失")
        
        # 检查缺失的字段是否补全
        if 'average_waiting_count' in sim_results['network_metrics']['pedestrian_metrics']:
            print("✅ 缺失的 pedestrian_metrics.average_waiting_count 已补全")
        else:
            print("❌ pedestrian_metrics.average_waiting_count 未补全")
        
        if 'vip_vehicle_metrics' in sim_results['network_metrics']:
            print("✅ 缺失的 vip_vehicle_metrics 已补全")
        else:
            print("❌ vip_vehicle_metrics 未补全")
        
        if 'venue_area_metrics' in sim_results['network_metrics']:
            print("✅ 缺失的 venue_area_metrics 已补全")
        else:
            print("❌ venue_area_metrics 未补全")
        
        if 'selected_edge_metrics' in sim_results:
            print("✅ 缺失的 selected_edge_metrics 已补全")
        else:
            print("❌ selected_edge_metrics 未补全")
            
    else:
        print(f"❌ 保存失败: {result.get('error', '未知错误')}")

if __name__ == "__main__":
    test_partial_merge()
