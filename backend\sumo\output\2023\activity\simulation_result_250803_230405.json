{"simulation_id": "250803_230405", "config": {"network_config": {"type": "predefined", "entrance_plan": "仅开放东侧出入口", "road_restriction": {"enabled": true, "vehicle_restricted_edges": ["edge1", "edge2"], "pedestrian_restricted_edges": ["edge3"]}}, "signal_config": {"type": "predefined", "optimization": {"enabled": true, "selected_intersections": ["int1", "int2"]}}, "traffic_config": {"type": "predefined", "scenario": "进场", "vehicle_type": "仅一般车辆", "traffic_scale": "medium", "vip_priority": {"enabled": false}}}, "config_summary": {"network": "预设路网 - 仅开放东侧出入口 + 道路限行(3个路段)", "signal": "预设配时 + 自定义优化(2个交叉口)", "traffic": "预设需求 - 进场场景 + 仅一般车辆"}, "start_time": "2025-08-03T23:04:05.220383", "simulation_results": {"network_metrics": {"pedestrian_metrics": {"average_travel_time": 120.5, "average_waiting_time": 30.2, "average_waiting_count": 2.1, "average_time_loss": 25.8}, "vehicle_metrics": {"average_travel_time": 180.3, "average_waiting_time": 45.7, "average_waiting_count": 3.2, "average_time_loss": 40.1}}}}