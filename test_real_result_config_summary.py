#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试真实仿真结果文件的配置摘要
"""

import sys
import os
sys.path.append('backend')

from config_parser import ConfigParser
import json

def test_real_result_config_summary():
    """测试真实仿真结果文件的配置摘要"""
    
    print("=== 测试真实仿真结果文件的配置摘要 ===")
    
    # 测试文件路径
    result_file = 'backend/sumo/output/2023/activity/simulation_result_250801162457.json'
    
    if not os.path.exists(result_file):
        print(f"❌ 结果文件不存在: {result_file}")
        return
    
    # 读取结果文件
    with open(result_file, 'r', encoding='utf-8') as f:
        result_data = json.load(f)
    
    print(f"仿真ID: {result_data['simulation_id']}")
    
    # 显示原始的config_summary
    print("\n当前的config_summary:")
    for key, value in result_data['config_summary'].items():
        print(f"  {key}: {value}")
    
    # 使用ConfigParser重新生成config_summary
    parser = ConfigParser()
    config = result_data['config']
    new_summary = parser.generate_config_summary(config)
    
    print("\n重新生成的config_summary:")
    for key, value in new_summary.items():
        print(f"  {key}: {value}")
    
    # 对比分析
    print("\n=== 对比分析 ===")
    for key in ['network', 'signal', 'traffic']:
        old_value = result_data['config_summary'][key]
        new_value = new_summary[key]
        
        if old_value != new_value:
            print(f"✅ {key}字段已改进:")
            print(f"    修改前: {old_value}")
            print(f"    修改后: {new_value}")
        else:
            print(f"⚪ {key}字段无变化: {old_value}")
    
    # 检查是否还有"等"字样
    print("\n=== 检查省略字样 ===")
    for key, value in new_summary.items():
        if '等' in value:
            print(f"⚠️  {key}字段仍包含'等'字样: {value}")
        else:
            print(f"✅ {key}字段不包含'等'字样")

if __name__ == '__main__':
    test_real_result_config_summary()
