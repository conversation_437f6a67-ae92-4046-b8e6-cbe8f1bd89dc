#!/usr/bin/env python3
"""
最终验证脚本：确认保存当前配置按钮的功能与backend\sumo\output\2023\activity的保存格式和内容相同
"""

import requests
import json
import os
from datetime import datetime

def main():
    print("=== 保存配置功能验证 ===\n")
    
    # 1. 测试保存功能
    print("1. 测试保存功能...")
    scheme_id = test_save_function()
    
    if not scheme_id:
        print("❌ 保存功能测试失败，终止验证")
        return
    
    # 2. 验证文件格式
    print(f"\n2. 验证保存的文件格式 (方案ID: {scheme_id})...")
    verify_file_format(scheme_id)
    
    # 3. 验证内容结构
    print(f"\n3. 验证文件内容结构...")
    verify_content_structure(scheme_id)
    
    print("\n=== 验证完成 ===")

def test_save_function():
    """测试保存功能"""
    
    # 构建测试配置
    test_config = {
        "network_config": {
            "type": "predefined",
            "file_path": None,
            "entrance_plan": "仅开放东侧出入口",
            "road_restriction": {
                "enabled": True,
                "vehicle_restricted_edges": ["-1307189273#2"],
                "pedestrian_restricted_edges": ["-1307189273#2"],
                "vehicle_restricted_edges_with_names": [
                    {"id": "-1307189273#2", "name": "-1307189273#2"}
                ],
                "pedestrian_restricted_edges_with_names": [
                    {"id": "-1307189273#2", "name": "-1307189273#2"}
                ]
            }
        },
        "signal_config": {
            "type": "predefined",
            "file_path": None,
            "optimization": {
                "enabled": True,
                "selected_intersections": ["cluster_11471925738_11471925739"],
                "selected_intersections_with_names": [
                    {"id": "cluster_11471925738_11471925739", "name": "交叉口11471925"}
                ]
            }
        },
        "traffic_config": {
            "type": "predefined",
            "file_path": None,
            "scenario": "进场",
            "vehicle_type": "仅一般车辆",
            "traffic_scale": "medium",
            "vip_priority": {"enabled": False}
        },
        "analysis_config": {
            "edge_analysis": {
                "enabled": True,
                "selected_edges": ["-1005075672#10"],
                "selected_edges_with_names": [
                    {"id": "-1005075672#10", "name": "易宁大街"}
                ]
            }
        }
    }
    
    # 构建测试仿真结果
    test_simulation_results = {
        "network_metrics": {
            "pedestrian_metrics": {
                "average_travel_time": 54.23,
                "average_waiting_time": 0.05,
                "average_waiting_count": 0.0,
                "average_time_loss": 8.36
            },
            "vehicle_metrics": {
                "average_travel_time": 300.49,
                "average_waiting_time": 83.43,
                "average_waiting_count": 4.23,
                "average_time_loss": 147.5
            },
            "vip_vehicle_metrics": {
                "average_travel_time": 0,
                "average_waiting_time": 0,
                "average_waiting_count": 0,
                "average_time_loss": 0
            },
            "venue_area_metrics": {
                "average_pedestrian_travel_time": 0,
                "average_pedestrian_delay": 0,
                "average_vehicle_travel_time": 0,
                "average_vehicle_delay": 0
            }
        },
        "selected_edge_metrics": {
            "pedestrian_metrics": {
                "total_departed": 0.0,
                "total_arrived": 0.0,
                "total_entered": 0.0,
                "avg_traveltime": 0,
                "avg_waitingTime": 0,
                "avg_speed": 0.0,
                "avg_timeLoss": 0,
                "avg_occupancy": 0.0
            },
            "vehicle_metrics": {
                "total_departed": 0,
                "total_arrived": 2.0,
                "total_entered": 52.0,
                "avg_traveltime": 6.075000000000001,
                "avg_waitingTime": 2.519230769230769,
                "avg_speed": 9.21,
                "avg_timeLoss": 4.884615384615385,
                "avg_occupancy": 0.0273
            }
        }
    }
    
    # 发送保存请求
    try:
        response = requests.post("http://localhost:8888/api/history/save", json={
            "config": test_config,
            "name": "最终验证测试",
            "description": "验证保存格式与activity目录一致",
            "simulation_results": test_simulation_results
        })
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print(f"✅ 保存成功，方案ID: {result['scheme_id']}")
                return result['scheme_id']
            else:
                print(f"❌ 保存失败: {result['error']}")
        else:
            print(f"❌ HTTP请求失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
    
    return None

def verify_file_format(scheme_id):
    """验证文件格式"""
    
    # 检查activity目录文件
    activity_file = f"backend/sumo/output/2023/activity/simulation_result_{scheme_id}.json"
    if os.path.exists(activity_file):
        print(f"✅ Activity目录文件存在: {activity_file}")
        
        # 检查文件格式
        with open(activity_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查缩进（应该是4个空格）
        lines = content.split('\n')
        indented_lines = [line for line in lines if line.startswith('    ')]
        if indented_lines:
            first_indent = len(indented_lines[0]) - len(indented_lines[0].lstrip())
            if first_indent == 4:
                print("✅ 使用4个空格缩进（与activity目录一致）")
            else:
                print(f"❌ 缩进不一致，使用{first_indent}个空格")
        
    else:
        print(f"❌ Activity目录文件不存在: {activity_file}")
    
    # 检查history目录文件
    history_file = f"backend/sumo_data/history/scheme_{scheme_id}.json"
    if os.path.exists(history_file):
        print(f"✅ History目录文件存在: {history_file}")
    else:
        print(f"❌ History目录文件不存在: {history_file}")

def verify_content_structure(scheme_id):
    """验证内容结构"""
    
    # 读取新保存的文件
    activity_file = f"backend/sumo/output/2023/activity/simulation_result_{scheme_id}.json"
    if not os.path.exists(activity_file):
        print("❌ 无法验证内容结构，文件不存在")
        return
    
    with open(activity_file, 'r', encoding='utf-8') as f:
        new_data = json.load(f)
    
    # 读取现有的参考文件
    reference_file = "backend/sumo/output/2023/activity/simulation_result_250801171330.json"
    if not os.path.exists(reference_file):
        print("❌ 参考文件不存在，无法对比")
        return
    
    with open(reference_file, 'r', encoding='utf-8') as f:
        ref_data = json.load(f)
    
    # 对比顶级字段
    new_keys = set(new_data.keys())
    ref_keys = set(ref_data.keys())
    
    if new_keys == ref_keys:
        print("✅ 顶级字段结构完全一致")
    else:
        print("❌ 顶级字段结构不一致")
        print(f"  参考文件字段: {sorted(ref_keys)}")
        print(f"  新文件字段: {sorted(new_keys)}")
        print(f"  缺少字段: {sorted(ref_keys - new_keys)}")
        print(f"  多余字段: {sorted(new_keys - ref_keys)}")
    
    # 检查必要字段
    required_fields = ['simulation_id', 'config', 'config_summary', 'start_time', 'simulation_results']
    missing_fields = [field for field in required_fields if field not in new_data]
    
    if not missing_fields:
        print("✅ 所有必要字段都存在")
    else:
        print(f"❌ 缺少必要字段: {missing_fields}")
    
    # 检查config结构
    if 'config' in new_data and 'config' in ref_data:
        new_config_keys = set(new_data['config'].keys())
        ref_config_keys = set(ref_data['config'].keys())
        
        if new_config_keys == ref_config_keys:
            print("✅ config字段结构一致")
        else:
            print("❌ config字段结构不一致")
    
    # 检查simulation_results结构
    if 'simulation_results' in new_data and 'simulation_results' in ref_data:
        new_results_keys = set(new_data['simulation_results'].keys())
        ref_results_keys = set(ref_data['simulation_results'].keys())
        
        if new_results_keys == ref_results_keys:
            print("✅ simulation_results字段结构一致")
        else:
            print("❌ simulation_results字段结构不一致")

if __name__ == "__main__":
    main()
