import xml.etree.ElementTree as ET
import os
import subprocess
import traci
import sumolib
import json
from datetime import datetime
from typing import Dict, Any

from config_parser import ConfigParser

class SimulationOrchestrator:
    """仿真编排器，负责根据新的JSON配置格式运行仿真"""
    
    def __init__(self):
        self.base_dir = './sumo_data'
        self.template_dir = os.path.join(self.base_dir, 'templates')
        self.tools_path = './tools'  # SUMO tools路径
        self.config_parser = ConfigParser()
    
    def run_simulation(self, config: Dict[str, Any]) -> str:
        """
        运行SUMO仿真
        
        参数:
        config: dict - 新格式的JSON配置
        
        返回:
        str - 仿真ID
        """
        # 1. 创建临时工作目录
        sim_id = self._create_temp_directory()
        
        print(f"开始仿真，ID: {sim_id}")
        
        try:
            # 2. 生成/准备路由文件
            route_file = self._prepare_route_file(sim_id, config)
            
            # 3. 生成sumocfg文件
            sumocfg_file = self._generate_sumocfg_file(sim_id, config, route_file)
            
            # 4. 启动并运行仿真
            self._run_sumo_simulation(sim_id, config, sumocfg_file)
            
            print(f"仿真完成，ID: {sim_id}")
            return sim_id
            
        except Exception as e:
            print(f"仿真失败: {str(e)}")
            raise
    
    def _create_temp_directory(self) -> str:
        """创建临时工作目录"""
        # 使用年月日时分秒格式创建目录名
        sim_id = datetime.now().strftime("%y%m%d%H%M%S")
        
        # 创建目录
        temp_dir = os.path.join(self.base_dir, sim_id)
        os.makedirs(temp_dir, exist_ok=True)
        
        # 创建weight子目录
        weight_dir = os.path.join(temp_dir, 'weight')
        os.makedirs(weight_dir, exist_ok=True)
        
        return sim_id
    
    def _prepare_route_file(self, sim_id: str, config: Dict[str, Any]) -> str:
        """准备路由文件"""
        # 检查是否使用自定义路由文件
        custom_route_file = self.config_parser.get_route_file(config)
        
        if custom_route_file:
            # 使用自定义路由文件
            print(f"使用自定义路由文件: {custom_route_file}")
            return custom_route_file
        else:
            # 生成预设交通流
            print("生成预设交通流...")
            return self._generate_predefined_traffic(sim_id, config)
    
    def _generate_predefined_traffic(self, sim_id: str, config: Dict[str, Any]) -> str:
        """生成预设交通流"""
        temp_dir = os.path.join(self.base_dir, sim_id)
        
        # 1. 确定使用的路网文件（如果启用限行，先创建限行路网）
        net_file = self._get_network_file_for_traffic_generation(sim_id, config)
        
        # 2. 生成背景交通流
        self._generate_background_traffic(sim_id, config, net_file)
        
        # 3. 生成活动交通流
        self._generate_activity_traffic(sim_id, config, net_file)
        
        # 4. 合并交通流
        final_route_file = self._merge_traffic_flows(sim_id, config)
        
        # 5. 如果是进场场景，添加停车信息
        if self.config_parser.get_scenario_type(config) == '进场':
            self._add_parking_stops(final_route_file)
        
        return final_route_file
    
    def _get_network_file_for_traffic_generation(self, sim_id: str, config: Dict[str, Any]) -> str:
        """获取用于交通流生成的路网文件"""
        # 获取原始路网文件
        original_net_file = self.config_parser.get_network_file(config)
        
        # 如果启用道路限行，创建并返回限行路网文件
        if self.config_parser.has_any_restriction_enabled(config):
            print("创建限行路网用于交通流生成...")
            return self._create_restricted_network(sim_id, original_net_file, config)
        else:
            return original_net_file
    
    def _generate_background_traffic(self, sim_id: str, config: Dict[str, Any], net_file: str):
        """生成背景交通流"""
        temp_dir = os.path.join(self.base_dir, sim_id)
        
        # 清理旧的背景交通流文件
        for file in os.listdir(temp_dir):
            if file.startswith('background'):
                os.remove(os.path.join(temp_dir, file))
        
        if not self.config_parser.has_any_restriction_enabled(config):
            # 无限行时，生成正常交通流
            # 生成小汽车交通流
            passenger_out = os.path.join(temp_dir, 'background_passenger.rou.xml')
            subprocess.run([
                'python',
                os.path.join(self.tools_path, 'randomTrips.py'),
                '-n', net_file,
                '-r', passenger_out,
                '--vehicle-class', 'passenger',
                '--prefix', 'background_passenger'
            ], check=True)
            
            # 生成行人交通流
            pedestrian_out = os.path.join(temp_dir, 'background_pedestrian.rou.xml')
            subprocess.run([
                'python',
                os.path.join(self.tools_path, 'randomTrips.py'),
                '-n', net_file,
                '-r', pedestrian_out,
                '--vehicle-class', 'pedestrian',
                '--prefix', 'background_pedestrian',
                '--pedestrians',
                '-p', '0.5',
                '--min-distance', '300'
            ], check=True)
        else:
            # 有限行时，使用限行路网生成背景交通流
            print("使用限行路网生成背景交通流...")
            
            # 生成行人交通流（行人不受限行影响）
            pedestrian_out = os.path.join(temp_dir, 'background_pedestrian.rou.xml')
            subprocess.run([
                'python',
                os.path.join(self.tools_path, 'randomTrips.py'),
                '-n', net_file,
                '-r', pedestrian_out,
                '--vehicle-class', 'pedestrian',
                '--prefix', 'pedestrian',
                '--pedestrians',
                '-p', '0.5',
                '--min-distance', '300'
            ], check=True)
            
            # 生成私家车交通流（会自动避开限行路段）
            private_out = os.path.join(temp_dir, 'background_private.rou.xml')
            subprocess.run([
                'python',
                os.path.join(self.tools_path, 'randomTrips.py'),
                '-n', net_file,
                '-r', private_out,
                '--vehicle-class', 'private',
                '--prefix', 'private'
            ], check=True)
        
        # 合并所有生成的路由文件到background.rou.xml
        self._merge_background_route_files(sim_id)
    
    def _merge_background_route_files(self, sim_id: str):
        """合并临时目录中的所有背景路由文件到background.rou.xml"""
        temp_dir = os.path.join(self.base_dir, sim_id)
        output_file = os.path.join(temp_dir, 'background.rou.xml')
        
        # 获取所有.rou.xml文件
        input_files = [f for f in os.listdir(temp_dir) if f.endswith('.rou.xml') 
                      and f.startswith('background_')]
        
        root = ET.Element('routes')
        vehicles = []
        
        # 合并所有路由文件
        for file_name in input_files:
            file_path = os.path.join(temp_dir, file_name)
            tree = ET.parse(file_path)
            for elem in tree.getroot():
                if elem.tag in ['vehicle', 'person']:
                    vehicles.append(elem)
                else:
                    root.append(elem)
        
        # 按出发时间排序
        vehicles.sort(key=lambda v: float(v.get('depart')))
        
        # 添加排序后的车辆
        for vehicle in vehicles:
            root.append(vehicle)
            
        # 保存合并后的文件
        tree = ET.ElementTree(root)
        tree.write(output_file, encoding='utf-8', xml_declaration=True)
    
    def _generate_activity_traffic(self, sim_id: str, config: Dict[str, Any], net_file: str):
        """生成活动相关的交通流"""
        # 创建weight文件夹
        weight_dir = os.path.join(self.base_dir, sim_id, 'weight')
        
        # 如果weight文件夹已存在，先清空它
        if os.path.exists(weight_dir):
            # 删除所有现有的权重文件
            for file in os.listdir(weight_dir):
                if file.endswith('.src.xml') or file.endswith('.dst.xml'):
                    os.remove(os.path.join(weight_dir, file))
        else:
            # 创建weight文件夹
            os.makedirs(weight_dir)
        
        # 创建车辆和行人的权重文件
        self._create_vehicle_weights(weight_dir, config)
        self._create_pedestrian_weights(weight_dir, config)
        
        # 生成活动相关的车辆和行人流
        self._generate_activity_routes(sim_id, config, net_file)
    
    def _create_vehicle_weights(self, weight_dir: str, config: Dict[str, Any]):
        """创建车辆权重文件"""
        scenario_type = self.config_parser.get_scenario_type(config)
        
        # 根据进场/离场场景确定文件名
        if scenario_type == '进场':
            weight_file = os.path.join(weight_dir, 'weight_vehicle.dst.xml')
        else:  # 离场
            weight_file = os.path.join(weight_dir, 'weight_vehicle.src.xml')
        
        # 创建权重文件的XML结构
        root = ET.Element('edgedata')
        interval = ET.SubElement(root, 'interval')
        interval.set('begin', '0')
        interval.set('end', '3600')
        
        # 根据场景设置权重
        entrance_plan = self.config_parser.get_entrance_plan(config)
        if entrance_plan == '仅开放东侧出入口':
            # 添加east_1到east_12的权重
            for i in range(1, 13):
                edge = ET.SubElement(interval, 'edge')
                edge.set('id', f'east_{i}')
                edge.set('value', '2')
        elif entrance_plan == '仅开放南侧出入口':
            # 添加south_1到south_24的权重
            for i in range(1, 25):
                edge = ET.SubElement(interval, 'edge')
                edge.set('id', f'south_{i}')
                edge.set('value', '1')
        else:  # 全部开放
            # 添加east_1到east_12的权重
            for i in range(1, 13):
                edge = ET.SubElement(interval, 'edge')
                edge.set('id', f'east_{i}')
                edge.set('value', '2')
            
            # 添加south_1到south_24的权重
            for i in range(1, 25):
                edge = ET.SubElement(interval, 'edge')
                edge.set('id', f'south_{i}')
                edge.set('value', '1')
        
        # 保存格式化后的权重文件
        self._save_xml_file(root, weight_file)
    
    def _create_pedestrian_weights(self, weight_dir: str, config: Dict[str, Any]):
        """创建行人权重文件"""
        scenario_type = self.config_parser.get_scenario_type(config)
        
        # 根据进场/离场场景确定文件名
        if scenario_type == '进场':
            weight_file = os.path.join(weight_dir, 'weight_pedestrian.dst.xml')
        else:  # 离场
            weight_file = os.path.join(weight_dir, 'weight_pedestrian.src.xml')
        
        # 创建权重文件的XML结构
        root = ET.Element('edgedata')
        interval = ET.SubElement(root, 'interval')
        interval.set('begin', '0')
        interval.set('end', '3600')
        
        # 根据场景设置权重
        entrance_plan = self.config_parser.get_entrance_plan(config)
        if entrance_plan == '仅开放东侧出入口':
            edge = ET.SubElement(interval, 'edge')
            if scenario_type == '进场':
                edge.set('id', 'people_east_in')
            else:
                edge.set('id', 'people_east_out')
            edge.set('value', '1')
        elif entrance_plan == '仅开放南侧出入口':
            edge = ET.SubElement(interval, 'edge')
            if scenario_type == '进场':
                edge.set('id', 'people_south_in')
            else:
                edge.set('id', 'people_south_out')
            edge.set('value', '1')
        else:  # 全部开放
            # 添加东侧入口/出口
            edge1 = ET.SubElement(interval, 'edge')
            if scenario_type == '进场':
                edge1.set('id', 'people_east_in')
            else:
                edge1.set('id', 'people_east_out')
            edge1.set('value', '1')
            
            # 添加南侧入口/出口
            edge2 = ET.SubElement(interval, 'edge')
            if scenario_type == '进场':
                edge2.set('id', 'people_south_in')
            else:
                edge2.set('id', 'people_south_out')
            edge2.set('value', '1')
        
        # 保存格式化后的权重文件
        self._save_xml_file(root, weight_file)
    
    def _save_xml_file(self, root, file_path):
        """保存格式化的XML文件"""
        xml_str = ET.tostring(root, encoding='unicode')
        from xml.dom import minidom
        pretty_xml = minidom.parseString(xml_str).toprettyxml(indent='    ')
        
        # 移除空行
        pretty_xml = '\n'.join([line for line in pretty_xml.split('\n') if line.strip()])
        
        # 保存文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(pretty_xml)
    
    def _generate_activity_routes(self, sim_id: str, config: Dict[str, Any], net_file: str):
        """生成活动相关的路由文件"""
        temp_dir = os.path.join(self.base_dir, sim_id)
        weight_dir = os.path.join(temp_dir, 'weight')
        
        # 生成车辆和行人路由文件
        route_configs = [
            ('gym_vehicle', False, 2, 'weight_vehicle'),
            ('gym_people', True, 0.3, 'weight_pedestrian')
        ]
        
        for prefix, is_person, p, weight_prefix in route_configs:
            out_file = os.path.join(temp_dir, f'{prefix}.rou.xml')
            cmd = [
                'python',
                os.path.join(self.tools_path, 'randomTrips.py'),
                '-n', net_file,
                '-r', out_file,
                '--vehicle-class', 'pedestrian' if is_person else 'passenger',
                '--prefix', prefix,
                '-p', str(p),
                '--weights-prefix', os.path.join(weight_dir, weight_prefix)
            ]
            if is_person:
                cmd.append('--pedestrians')
                cmd.append('--min-distance')
                cmd.append('300')
            subprocess.run(cmd, check=True)
    
    def _merge_traffic_flows(self, sim_id: str, config: Dict[str, Any]) -> str:
        """合并所有交通流文件"""
        temp_dir = os.path.join(self.base_dir, sim_id)
        output_file = os.path.join(temp_dir, 'routes.rou.xml')
        
        # 获取所有需要合并的文件
        input_files = []
        
        # 添加背景交通流文件
        background_file = os.path.join(temp_dir, 'background.rou.xml')
        if os.path.exists(background_file):
            input_files.append(background_file)
        
        # 添加活动相关的交通流文件
        activity_prefixes = ['gym_vehicle', 'gym_people']
        for prefix in activity_prefixes:
            activity_file = os.path.join(temp_dir, f'{prefix}.rou.xml')
            if os.path.exists(activity_file):
                input_files.append(activity_file)
        
        # 创建合并后的根元素
        root = ET.Element('routes')
        
        # 如果是VIP场景，添加VIP车辆类型定义和VIP车辆
        if self.config_parser.is_vip_scenario(config):
            # 添加VIP车辆类型
            vip_type = ET.SubElement(root, 'vType')
            vip_type.set('id', 'vip_car')
            vip_type.set('vClass', 'custom1')  # 设置为custom1以便通过限行路段
            vip_type.set('color', '0,1,0')
            
            # 添加VIP车辆
            vip_vehicle = ET.SubElement(root, 'vehicle')
            vip_vehicle.set('id', 'vip')
            vip_vehicle.set('type', 'vip_car')
            vip_vehicle.set('depart', '0.00')
            
            # 添加VIP车辆路由
            vip_route = ET.SubElement(vip_vehicle, 'route')
            scenario_type = self.config_parser.get_scenario_type(config)
            if scenario_type == '进场':
                vip_route.set('edges', "1192568455#8 -1127568452#2 -1127568452#1 -1127568452#0 1127568453#0 1127568453#1 1127568453#2 1127568453#3 -1091134078#13 -1091134078#12 -1091134078#11 -1091134078#10 1225157743#6 1225157743#7 1225157743#8 1225157743#9 1225157743#11 1225157743#12 980398775#10 980398780#1 980398780#1.18 E0 E0.21 people_east_in")
            else:  # 离场
                vip_route.set('edges', "people_south_out -E11.15 -E11.33 -E11.57 -E11.84 -E11.114 -E11.145 -1307189273#2.153 -1307189273#2.250 -1307189273#1 1077433155#2 1077433155#4 1077433155#5 1077433155#6 1077433155#7 1091134078#10 1091134078#11 1091134078#12 1091134078#13 -1127568453#3 -1127568453#2 -1127568453#1 -1127568453#0 1127568452#0 1127568452#1 1127568452#2 1192568456#6")
        
        # 用于存储所有vehicle和person元素
        vehicles = []
        
        # 遍历所有输入文件
        for file_name in input_files:
            tree = ET.parse(file_name)
            for elem in tree.getroot():
                if elem.tag in ['vehicle', 'person']:
                    vehicles.append(elem)
                else:
                    # 非vehicle/person元素（如vType）直接添加到root
                    root.append(elem)
        
        # 按出发时间排序
        vehicles.sort(key=lambda v: float(v.get('depart')))
        
        # 将排序后的vehicle/person元素添加到root
        for vehicle in vehicles:
            root.append(vehicle)
        
        # 保存合并后的文件
        tree = ET.ElementTree(root)
        tree.write(output_file, encoding='utf-8', xml_declaration=True)
        
        return output_file
    
    def _add_parking_stops(self, route_file: str):
        """为体育馆车辆添加停车信息"""
        # 读取路由文件
        tree = ET.parse(route_file)
        root = tree.getroot()
        
        # 遍历所有vehicle节点
        for vehicle in root.findall('vehicle'):
            if vehicle.get('id').startswith('gym_vehicle'):
                route = vehicle.find('route')
                if route is not None:
                    edges = route.get('edges')
                    if edges and not vehicle.find('stop'):
                        last_edge = edges.split()[-1]
                        
                        # 检查是否是east_1到east_12或south_1到south_24
                        if last_edge.startswith('east_') and last_edge[5:].isdigit():
                            edge_num = int(last_edge[5:])
                            if 1 <= edge_num <= 12:
                                stop = ET.SubElement(vehicle, 'stop')
                                stop.set('parkingArea', f'pa_{last_edge}')
                                stop.set('duration', '3600')
                        
                        elif last_edge.startswith('south_') and last_edge[6:].isdigit():
                            edge_num = int(last_edge[6:])
                            if 1 <= edge_num <= 24:
                                stop = ET.SubElement(vehicle, 'stop')
                                stop.set('parkingArea', f'pa_{last_edge}')
                                stop.set('duration', '3600')
        
        # 保存修改后的文件
        tree.write(route_file, encoding='utf-8', xml_declaration=True)
    
    def _generate_sumocfg_file(self, sim_id: str, config: Dict[str, Any], route_file: str) -> str:
        """
        生成SUMO配置文件
        
        参数:
        sim_id: str - 仿真ID
        config: dict - 配置信息
        route_file: str - 路由文件路径
        
        返回:
        str - 生成的sumocfg文件路径
        """
        temp_dir = os.path.join(self.base_dir, sim_id)
        sumocfg_path = os.path.join(temp_dir, 'simulation.sumocfg')
        
        # 获取路网文件 - 使用与交通流生成相同的逻辑
        net_file = self._get_final_network_file(sim_id, config)
        
        # 确定附加文件
        additional_files = [
            os.path.join(self.template_dir, 'osm_polygons.add.xml'),
            os.path.join(self.template_dir, 'parkinglot.add.xml')
        ]
        
        # 处理edge_data.add.xml文件
        edge_data_needed = False
        
        # 如果是离场场景，需要edge_data.add.xml来计算路网级离场指标
        if self.config_parser.get_scenario_type(config) == '离场':
            edge_data_needed = True
        
        # 如果启用了用户选择路段分析，也需要edge_data.add.xml
        if config.get('analysis_config', {}).get('edge_analysis', {}).get('enabled', False):
            selected_edges = config['analysis_config']['edge_analysis'].get('selected_edges', [])
            if selected_edges:
                edge_data_needed = True
        
        if edge_data_needed:
            # 读取原始edge_data.add.xml模板
            edge_data_template = os.path.join(self.template_dir, 'edge_data.add.xml')
            edge_data_output = os.path.join(temp_dir, 'edge_data.add.xml')
            
            # 创建edge_data.add.xml文件
            if os.path.exists(edge_data_template):
                # 如果有模板文件，解析并修改
                tree = ET.parse(edge_data_template)
                root = tree.getroot()
                
                # 修改原有edgeData元素的file属性（用于路网级指标）
                for edge_data in root.findall('edgeData'):
                    file_path = edge_data.get('file')
                    if file_path:
                        file_name = os.path.basename(file_path)
                        edge_data.set('file', file_name)
            else:
                # 如果没有模板文件，创建新的XML
                root = ET.Element('additionalFile')
            
            # 如果启用了用户选择路段分析，添加相应的edgeData元素
            if config.get('analysis_config', {}).get('edge_analysis', {}).get('enabled', False):
                selected_edges = config['analysis_config']['edge_analysis'].get('selected_edges', [])
                if selected_edges:
                    # 为用户选择的路段添加新的edgeData元素
                    edges_str = ' '.join(selected_edges)
                    
                    # 添加车辆数据收集
                    vehicle_edge_data = ET.SubElement(root, 'edgeData')
                    vehicle_edge_data.set('id', 'selected_vehicle')
                    vehicle_edge_data.set('file', 'selected_vehicle_edgedata.xml')
                    vehicle_edge_data.set('edges', edges_str)
                    
                    # 添加行人数据收集
                    people_edge_data = ET.SubElement(root, 'edgeData')
                    people_edge_data.set('id', 'selected_people')
                    people_edge_data.set('file', 'selected_people_edgedata.xml')
                    people_edge_data.set('edges', edges_str)
                    people_edge_data.set('detectPersons', 'walk')
                    
                    print(f"为用户选择的路段添加数据收集: {selected_edges}")
            
            # 保存文件
            tree = ET.ElementTree(root)
            tree.write(edge_data_output, encoding='utf-8', xml_declaration=True)
            
            # 使用生成的文件
            additional_files.append(edge_data_output)
        
        # 处理信号配时文件
        if self.config_parser.is_signal_optimization_enabled(config):
            # 生成自定义优化的配时方案
            optimized_signal_file = self._generate_optimized_signal_plan(sim_id, config, route_file)
            additional_files.append(optimized_signal_file)
        else:
            # 使用预设信号配时文件
            signal_files = self.config_parser.get_signal_files(config)
            additional_files.extend(signal_files)
        
        # 创建sumocfg文件
        root = ET.Element('configuration')
        
        # 添加输入配置
        input_section = ET.SubElement(root, 'input')
        
        # 添加网络文件
        net_file_elem = ET.SubElement(input_section, 'net-file')
        net_file_elem.set('value', os.path.abspath(net_file))
        
        # 添加路由文件
        route_file_elem = ET.SubElement(input_section, 'route-files')
        route_file_elem.set('value', os.path.abspath(route_file))
        
        # 添加附加文件
        if additional_files:
            additional_elem = ET.SubElement(input_section, 'additional-files')
            additional_elem.set('value', ','.join([os.path.abspath(f) for f in additional_files]))
        
        # 添加GUI设置文件
        gui_file_elem = ET.SubElement(input_section, 'gui-settings-file')
        gui_file_elem.set('value', os.path.abspath(os.path.join(self.template_dir, 'style.xml')))
        
        # 添加输出配置
        output_section = ET.SubElement(root, 'output')
        
        # 添加输出前缀
        output_prefix_elem = ET.SubElement(output_section, 'output-prefix')
        output_prefix_elem.set('value', 'output.')
        
        # 添加摘要输出
        summary_elem = ET.SubElement(output_section, 'summary')
        summary_elem.set('value', os.path.abspath(os.path.join(temp_dir, 'summary.xml')))
        
        # 添加行人摘要输出
        person_summary_elem = ET.SubElement(output_section, 'person-summary-output')
        person_summary_elem.set('value', os.path.abspath(os.path.join(temp_dir, 'person.summary.xml')))
        
        # 添加行程信息输出
        tripinfo_elem = ET.SubElement(output_section, 'tripinfo-output')
        tripinfo_elem.set('value', os.path.abspath(os.path.join(temp_dir, 'tripinfo.xml')))
        
        # 添加未完成行程信息输出
        tripinfo_unfinished_elem = ET.SubElement(output_section, 'tripinfo-output.write-unfinished')
        tripinfo_unfinished_elem.set('value', 'true')
        
        # 添加统计信息输出
        statistics_elem = ET.SubElement(output_section, 'statistic-output')
        statistics_elem.set('value', os.path.abspath(os.path.join(temp_dir, 'statistics.xml')))
        
        # 添加时间配置
        time_section = ET.SubElement(root, 'time')
        
        # 添加开始时间
        begin_elem = ET.SubElement(time_section, 'begin')
        begin_elem.set('value', '0')
        
        # 添加结束时间
        end_elem = ET.SubElement(time_section, 'end')
        end_elem.set('value', '3600')
        
        # 添加步长
        step_length_elem = ET.SubElement(time_section, 'step-length')
        step_length_elem.set('value', '1')
        
        # 添加处理配置
        processing_section = ET.SubElement(root, 'processing')
        
        # 添加种子
        seed_elem = ET.SubElement(processing_section, 'seed')
        seed_elem.set('value', '42')
        
        # 添加交通规模
        scale_elem = ET.SubElement(processing_section, 'scale')
        scale_elem.set('value', str(self.config_parser.get_traffic_scale(config)))
        
        # 添加忽略路由错误
        ignore_route_errors_elem = ET.SubElement(processing_section, 'ignore-route-errors')
        ignore_route_errors_elem.set('value', 'true')
        
        # 添加报告配置
        report_section = ET.SubElement(root, 'report')
        
        # 添加详细输出
        verbose_elem = ET.SubElement(report_section, 'verbose')
        verbose_elem.set('value', 'true')
        
        # 添加步骤日志
        no_step_log_elem = ET.SubElement(report_section, 'no-step-log')
        no_step_log_elem.set('value', 'false')
        
        # 添加持续时间日志统计
        duration_log_statistics_elem = ET.SubElement(report_section, 'duration-log.statistics')
        duration_log_statistics_elem.set('value', 'true')
        
        # 添加禁用持续时间日志
        duration_log_disable_elem = ET.SubElement(report_section, 'duration-log.disable')
        duration_log_disable_elem.set('value', 'false')
        
        # 添加警告
        no_warnings_elem = ET.SubElement(report_section, 'no-warnings')
        no_warnings_elem.set('value', 'false')
        
        # 保存sumocfg文件
        tree = ET.ElementTree(root)
        tree.write(sumocfg_path, encoding='utf-8', xml_declaration=True)
        
        print(f"生成SUMO配置文件: {sumocfg_path}")
        return sumocfg_path
    
    def _run_sumo_simulation(self, sim_id: str, config: Dict[str, Any], sumocfg_file: str):
        """运行SUMO仿真并保存每帧数据"""
        # 使用sumocfg文件启动仿真
        sumo_cmd = ['sumo', '-c', sumocfg_file]

        # 启动仿真
        traci.start(sumo_cmd)

        # 创建帧数据存储文件
        frames_file = os.path.join(self.base_dir, sim_id, 'simulation_frames.json')

        try:
            if self.config_parser.is_vip_priority_enabled(config):
                # VIP优先通行仿真
                self._run_vip_simulation_with_recording(sim_id, frames_file)
            else:
                # 普通仿真
                self._run_normal_simulation_with_recording(sim_id, frames_file)
        finally:
            # 确保关闭仿真
            traci.close()
    
    def _run_vip_simulation(self):
        """运行VIP优先通行仿真"""
        vip_car_id = 'vip'
        last_real_edge = None
        distance_threshold = 50  # 距离路口50米时开始设置绿灯
        current_tls = None  # 当前正在控制的信号灯
        
        while traci.simulation.getTime() < 600:
            vehicle_ids = traci.vehicle.getIDList()
            if vip_car_id in vehicle_ids:
                current_edge = traci.vehicle.getRoadID(vip_car_id)
                
                # 如果当前在内部道路（路口内），使用上一个实际的edge
                if current_edge.startswith(':'):
                    if last_real_edge is None:
                        continue
                    current_edge = last_real_edge
                else:
                    last_real_edge = current_edge
                
                try:
                    route = traci.vehicle.getRoute(vip_car_id)
                    current_index = route.index(current_edge)
                    
                    if current_index < len(route) - 1:
                        next_edge = route[current_index + 1]
                        
                        # 获取当前edge通往的junction
                        to_junction = traci.edge.getToJunction(current_edge)
                        
                        # 获取车辆到路口的距离
                        remaining_dist = traci.vehicle.getLanePosition(vip_car_id)
                        lane_length = traci.lane.getLength(current_edge + "_0")
                        distance_to_tls = lane_length - remaining_dist
                        
                        try:
                            # 获取当前车道的所有控制连接
                            controlled_links = traci.trafficlight.getControlledLinks(to_junction)
                            if controlled_links:  # 如果是信号灯控制的路口
                                # 获取当前车道ID
                                current_lane = traci.vehicle.getLaneID(vip_car_id)
                                next_lane = next_edge + "_0"  # 假设使用目标edge的第一条车道
                                
                                # 在控制连接中找到当前路径对应的连接
                                for i, link_group in enumerate(controlled_links):
                                    for link in link_group:
                                        if link[0] == current_lane and link[1].split("_")[0] == next_edge:
                                            # 找到了对应的连接，i就是在相位中的索引位置
                                            if distance_to_tls <= distance_threshold:
                                                if current_tls != to_junction:
                                                    current_tls = to_junction
                                                
                                                # 获取当前相位的状态
                                                current_state = list(traci.trafficlight.getRedYellowGreenState(to_junction))
                                                
                                                # 如果当前不是绿灯，找到合适的绿灯相位
                                                if current_state[i] != 'G':
                                                    phases = traci.trafficlight.getAllProgramLogics(to_junction)[0].phases
                                                    for phase_index, phase in enumerate(phases):
                                                        if phase.state[i] == 'G':
                                                            traci.trafficlight.setPhase(to_junction, phase_index)
                                                            break
                                            elif current_tls == to_junction:
                                                current_tls = None
                                            break
                        except traci.exceptions.TraCIException:
                            # 路口没有信号灯，跳过处理
                            pass
                
                except ValueError:
                    print(f"--警告: edge {current_edge} 不在路由中")
                    continue
            
            traci.simulationStep()

    def _run_normal_simulation(self):
        """运行普通仿真"""
        while traci.simulation.getTime() < 600:
            traci.simulationStep()

    def _run_normal_simulation_with_recording(self, sim_id: str, frames_file: str):
        """运行普通仿真并记录每帧数据"""
        step = 0
        max_steps = 600
        all_frames = []

        print(f"开始记录仿真数据到: {frames_file}")

        while step < max_steps and traci.simulation.getMinExpectedNumber() > 0:
            # 执行一步仿真
            traci.simulationStep()
            step += 1

            # 收集当前帧数据
            frame_data = self._collect_frame_data(step)
            all_frames.append(frame_data)

            # 每100步打印一次进度
            if step % 100 == 0:
                print(f"已记录 {step} 帧数据")

        print(f"仿真记录完成，共 {step} 帧")

        # 保存所有帧数据到一个JSON文件
        simulation_data = {
            "simulation_id": sim_id,
            "total_frames": step,
            "max_steps": max_steps,
            "completed": True,
            "frames": all_frames
        }

        with open(frames_file, 'w', encoding='utf-8') as f:
            json.dump(simulation_data, f, ensure_ascii=False, indent=2)

        print(f"仿真数据已保存到: {frames_file}")

    def _run_vip_simulation_with_recording(self, sim_id: str, frames_file: str):
        """运行VIP优先通行仿真并记录每帧数据"""
        vip_car_id = 'vip'
        last_real_edge = None
        distance_threshold = 50  # 距离路口50米时开始设置绿灯
        current_tls = None  # 当前正在控制的信号灯
        step = 0
        max_steps = 600
        all_frames = []

        print(f"开始记录VIP仿真数据到: {frames_file}")

        while step < max_steps and traci.simulation.getMinExpectedNumber() > 0:
            vehicle_ids = traci.vehicle.getIDList()
            if vip_car_id in vehicle_ids:
                current_edge = traci.vehicle.getRoadID(vip_car_id)

                # 如果当前在内部道路（路口内），使用上一个实际的edge
                if current_edge.startswith(':'):
                    if last_real_edge is None:
                        continue
                    current_edge = last_real_edge
                else:
                    last_real_edge = current_edge

                try:
                    route = traci.vehicle.getRoute(vip_car_id)
                    current_index = route.index(current_edge)

                    if current_index < len(route) - 1:
                        next_edge = route[current_index + 1]

                        # 获取当前edge通往的junction
                        to_junction = traci.edge.getToJunction(current_edge)

                        # 获取车辆到路口的距离
                        remaining_dist = traci.vehicle.getLanePosition(vip_car_id)
                        lane_length = traci.lane.getLength(current_edge + "_0")
                        distance_to_tls = lane_length - remaining_dist

                        try:
                            # 获取当前车道的所有控制连接
                            controlled_links = traci.trafficlight.getControlledLinks(to_junction)
                            if controlled_links:  # 如果是信号灯控制的路口
                                # 获取当前车道ID
                                current_lane = traci.vehicle.getLaneID(vip_car_id)

                                # 在控制连接中找到当前路径对应的连接
                                for i, link_group in enumerate(controlled_links):
                                    for link in link_group:
                                        if link[0] == current_lane and link[1].split("_")[0] == next_edge:
                                            # 找到了对应的连接，i就是在相位中的索引位置
                                            if distance_to_tls <= distance_threshold:
                                                if current_tls != to_junction:
                                                    current_tls = to_junction

                                                # 获取当前相位的状态
                                                current_state = list(traci.trafficlight.getRedYellowGreenState(to_junction))

                                                # 如果当前不是绿灯，找到合适的绿灯相位
                                                if current_state[i] != 'G':
                                                    phases = traci.trafficlight.getAllProgramLogics(to_junction)[0].phases
                                                    for phase_index, phase in enumerate(phases):
                                                        if phase.state[i] == 'G':
                                                            traci.trafficlight.setPhase(to_junction, phase_index)
                                                            break
                                            elif current_tls == to_junction:
                                                current_tls = None
                                            break
                        except traci.exceptions.TraCIException:
                            # 路口没有信号灯，跳过处理
                            pass

                except ValueError:
                    print(f"--警告: edge {current_edge} 不在路由中")
                    continue

            # 执行一步仿真
            traci.simulationStep()
            step += 1

            # 收集当前帧数据
            frame_data = self._collect_frame_data(step)
            all_frames.append(frame_data)

            # 每100步打印一次进度
            if step % 100 == 0:
                print(f"已记录 {step} 帧VIP仿真数据")

        print(f"VIP仿真记录完成，共 {step} 帧")

        # 保存所有帧数据到一个JSON文件
        simulation_data = {
            "simulation_id": sim_id,
            "total_frames": step,
            "max_steps": max_steps,
            "simulation_type": "vip",
            "completed": True,
            "frames": all_frames
        }

        with open(frames_file, 'w', encoding='utf-8') as f:
            json.dump(simulation_data, f, ensure_ascii=False, indent=2)

        print(f"VIP仿真数据已保存到: {frames_file}")

    def _collect_frame_data(self, step: int) -> Dict[str, Any]:
        """收集当前帧的所有实体数据"""
        entities = []

        # 获取车辆信息
        vehicle_ids = traci.vehicle.getIDList()
        for veh_id in vehicle_ids:
            try:
                position = traci.vehicle.getPosition(veh_id)
                speed = traci.vehicle.getSpeed(veh_id)

                entities.append({
                    "id": veh_id,
                    "type": "vehicle",
                    "position": [position[0], position[1]],
                    "speed": speed
                })
            except traci.exceptions.TraCIException:
                # 车辆可能已经离开仿真
                continue

        # 获取行人信息
        person_ids = traci.person.getIDList()
        for person_id in person_ids:
            try:
                position = traci.person.getPosition(person_id)
                speed = traci.person.getSpeed(person_id)

                entities.append({
                    "id": person_id,
                    "type": "person",
                    "position": [position[0], position[1]],
                    "speed": speed
                })
            except traci.exceptions.TraCIException:
                # 行人可能已经离开仿真
                continue

        # 统计信息
        statistics = {
            "vehicle_count": len([e for e in entities if e["type"] == "vehicle"]),
            "person_count": len([e for e in entities if e["type"] == "person"]),
            "total_entities": len(entities)
        }

        return {
            "step": step,
            "timestamp": traci.simulation.getTime(),
            "entities": entities,
            "statistics": statistics
        }



    def _get_final_network_file(self, sim_id: str, config: Dict[str, Any]) -> str:
        """获取最终用于仿真的路网文件"""
        # 如果启用道路限行，返回限行路网文件路径
        if self.config_parser.has_any_restriction_enabled(config):
            temp_dir = os.path.join(self.base_dir, sim_id)
            restricted_net_file = os.path.join(temp_dir, 'restricted_network.net.xml')
            # 检查文件是否存在，如果不存在则创建
            if not os.path.exists(restricted_net_file):
                original_net_file = self.config_parser.get_network_file(config)
                return self._create_restricted_network(sim_id, original_net_file, config)
            return restricted_net_file
        else:
            return self.config_parser.get_network_file(config)
    
    def _create_restricted_network(self, sim_id: str, original_net_file: str, config: Dict[str, Any]) -> str:
        """
        创建限行路网文件
        
        参数:
        sim_id: str - 仿真ID
        original_net_file: str - 原始路网文件路径
        config: dict - 配置信息
        
        返回:
        str - 限行路网文件路径
        """
        temp_dir = os.path.join(self.base_dir, sim_id)
        restricted_net_file = os.path.join(temp_dir, 'restricted_network.net.xml')
        
        # 获取车辆和行人限行路段列表
        vehicle_restricted_edges = self.config_parser.get_vehicle_restricted_edges(config)
        pedestrian_restricted_edges = self.config_parser.get_pedestrian_restricted_edges(config)
        
        print(f"正在创建限行路网")
        print(f"车辆限行路段: {vehicle_restricted_edges}")
        print(f"行人限行路段: {pedestrian_restricted_edges}")
        
        try:
            # 解析原始路网文件
            tree = ET.parse(original_net_file)
            root = tree.getroot()
            
            # 修改限行路段的车道allow属性
            for edge in root.findall('edge'):
                edge_id = edge.get('id')
                
                # 检查是否需要应用限行
                is_vehicle_restricted = edge_id in vehicle_restricted_edges
                is_pedestrian_restricted = edge_id in pedestrian_restricted_edges
                
                if is_vehicle_restricted or is_pedestrian_restricted:
                    print(f"设置路段 {edge_id} 限行: 车辆={'是' if is_vehicle_restricted else '否'}, 行人={'是' if is_pedestrian_restricted else '否'}")
                    
                    # 修改该路段的所有车道
                    for lane in edge.findall('lane'):
                        allowed_vehicles = []
                        disallowed_vehicles = []
                        
                        # 处理车辆限行
                        if is_vehicle_restricted:
                            # 车辆限行：只允许VIP车辆(custom1)通过
                            allowed_vehicles.append('custom1')
                            disallowed_vehicles.extend(['passenger', 'private', 'taxi', 'bus', 'coach', 'delivery', 'truck', 'trailer', 'motorcycle', 'moped', 'bicycle'])
                        else:
                            # 不限行车辆：允许所有车辆类型
                            allowed_vehicles.extend(['passenger', 'private', 'taxi', 'bus', 'coach', 'delivery', 'truck', 'trailer', 'motorcycle', 'moped', 'bicycle', 'custom1'])
                        
                        # 处理行人限行
                        if not is_pedestrian_restricted:
                            # 不限行行人：允许行人通过
                            allowed_vehicles.append('pedestrian')
                        else:
                            # 限行行人：禁止行人通过
                            disallowed_vehicles.append('pedestrian')
                        
                        # 设置allow和disallow属性
                        if allowed_vehicles:
                            lane.set('allow', ' '.join(allowed_vehicles))
                        if disallowed_vehicles:
                            lane.set('disallow', ' '.join(disallowed_vehicles))
            
            # 处理行人限行：删除相关的人行道连接
            if pedestrian_restricted_edges:
                self._remove_pedestrian_connections(root, pedestrian_restricted_edges)
            
            # 保存修改后的路网文件
            tree.write(restricted_net_file, encoding='utf-8', xml_declaration=True)
            print(f"限行路网文件已创建: {restricted_net_file}")
            
            return restricted_net_file
            
        except Exception as e:
            print(f"创建限行路网失败: {e}")
            # 如果创建失败，返回原始路网文件
            return original_net_file
    
    def _remove_pedestrian_connections(self, root, pedestrian_restricted_edges):
        """
        删除与行人限行路段相关的人行道连接
        
        参数:
        root: xml.etree.ElementTree.Element - 路网文件根节点
        pedestrian_restricted_edges: List[str] - 行人限行路段列表
        """
        print(f"正在删除行人限行路段的相关连接...")
        
        connections_to_remove = []
        
        # 查找所有connection元素
        for connection in root.findall('connection'):
            from_edge = connection.get('from')
            to_edge = connection.get('to')
            
            # 检查连接是否涉及行人限行路段
            restricted_edge = None
            other_edge = None
            
            if from_edge in pedestrian_restricted_edges:
                restricted_edge = from_edge
                other_edge = to_edge
            elif to_edge in pedestrian_restricted_edges:
                restricted_edge = to_edge  
                other_edge = from_edge
            
            # 如果找到涉及限行路段的连接，检查另一端是否为人行道
            if restricted_edge and other_edge:
                if self._is_pedestrian_walkway(other_edge):
                    print(f"删除连接: from={from_edge}, to={to_edge} (涉及行人限行路段 {restricted_edge})")
                    connections_to_remove.append(connection)
        
        # 删除标记的连接
        for connection in connections_to_remove:
            root.remove(connection)
        
        print(f"已删除 {len(connections_to_remove)} 个行人通道连接")
    
    def _is_pedestrian_walkway(self, edge_id):
        """
        判断路段ID是否为人行道
        
        参数:
        edge_id: str - 路段ID
        
        返回:
        bool - 是否为人行道
        """
        if not edge_id:
            return False
            
        # 检查是否以:开头
        if not edge_id.startswith(':'):
            return False
        
        # 以_分割，检查最后一个部分是否以w开头
        parts = edge_id.split('_')
        if parts and len(parts) > 0:
            last_part = parts[-1]
            return last_part.startswith('w')
        
        return False
    
    def _generate_optimized_signal_plan(self, sim_id: str, config: Dict[str, Any], route_file: str) -> str:
        """生成优化的信号配时方案"""
        temp_dir = os.path.join(self.base_dir, sim_id)
        net_file = self.config_parser.get_network_file(config)
        
        # 获取需要优化的交叉口
        optimization_intersections = self.config_parser.get_optimization_intersections(config)
        
        # 获取所有交叉口ID（用于计算跳过的交叉口）
        all_tls_ids = self._get_all_tls_ids(net_file)
        skip_tls_ids = [tls_id for tls_id in all_tls_ids if tls_id not in optimization_intersections]
        
        # 生成优化后的配时文件
        optimized_file = os.path.join(temp_dir, 'optimized_signal.add.xml')
        
        # 构建tlsCycleAdaption.py命令
        cmd = [
            'python',
            'tools/tlsCycleAdaptation.py',
            '-n', net_file,
            '-r', route_file,
            '-g', '20',
            '-o', optimized_file
        ]
        
        # 添加跳过的路口ID
        if skip_tls_ids:
            cmd.extend(['--skip', ','.join(skip_tls_ids)])
        
        try:
            # 执行优化程序
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(f"信号优化完成: {result.stdout}")
            return optimized_file
        except subprocess.CalledProcessError as e:
            print(f"信号优化失败: {e.stderr}")
            # 如果优化失败，返回默认配时文件
            return os.path.join(self.template_dir, 'newtls.add.xml')
    
    def _get_all_tls_ids(self, net_file_path: str) -> list:
        """从路网文件中获取所有路口ID"""
        if not os.path.exists(net_file_path):
            return []
        
        # 使用traci临时启动仿真来获取路口ID
        temp_cmd = ['sumo', '-n', net_file_path, '--no-step-log', '--no-warnings']
        
        try:
            # 启动临时仿真
            traci.start(temp_cmd)
            # 获取所有信号灯路口ID
            tls_ids = list(traci.trafficlight.getIDList())
            # 关闭临时仿真
            traci.close()
            return tls_ids
        except Exception as e:
            print(f"获取路口ID失败: {e}")
            # 确保关闭traci连接
            try:
                traci.close()
            except:
                pass
            return []

    def get_network_geojson(self):
        """获取网络的GeoJSON数据"""
        try:
            # 使用默认网络文件
            net_file = os.path.join('sumo_data', 'templates', 'gym_tls.net.xml')

            if not os.path.exists(net_file):
                print(f"❌ 网络文件不存在: {net_file}")
                return {"type": "FeatureCollection", "features": []}

            # 加载SUMO网络
            sumo_net = sumolib.net.readNet(net_file)
            features = []

            # 添加道路
            for edge in sumo_net.getEdges():
                edge_id = edge.getID()
                if edge_id.startswith(":"):  # 跳过内部边
                    continue

                # 获取车道
                lanes = edge.getLanes()

                for lane in lanes:
                    lane_shape = lane.getShape()

                    # 创建车道多边形（将车道线转换为带宽度的多边形）
                    if len(lane_shape) >= 2:
                        # 使用统一宽度 3
                        lane_width = 3
                        polygon_points = self._create_lane_polygon(lane_shape, lane_width)

                        if polygon_points and len(polygon_points) >= 4:  # 确保有足够的点形成多边形
                            polygon_feature = {
                                "type": "Feature",
                                "geometry": {
                                    "type": "Polygon",
                                    "coordinates": [polygon_points]
                                },
                                "properties": {
                                    "id": lane.getID() + "_polygon",
                                    "edge": edge_id,
                                    "type": "lane_polygon"
                                }
                            }
                            features.append(polygon_feature)

            # 添加交叉口
            for junction in sumo_net.getNodes():
                if junction.getType() not in ['priority', 'traffic_light']:
                    continue

                shape = junction.getShape()
                if shape:
                    coordinates = []
                    for x, y in shape:
                        coordinates.append([x, y])

                    # 闭合多边形
                    if coordinates and coordinates[0] != coordinates[-1]:
                        coordinates.append(coordinates[0])

                    feature = {
                        "type": "Feature",
                        "geometry": {
                            "type": "Polygon",
                            "coordinates": [coordinates]
                        },
                        "properties": {
                            "id": junction.getID(),
                            "type": "junction",
                            "junction_type": junction.getType()
                        }
                    }
                    features.append(feature)

            print(f"✅ 网络转换完成，包含 {len(features)} 个特征")
            return {"type": "FeatureCollection", "features": features}

        except Exception as e:
            print(f"❌ 获取网络GeoJSON失败: {e}")
            import traceback
            traceback.print_exc()
            return {"type": "FeatureCollection", "features": []}

    def _create_lane_polygon(self, shape, width):
        """创建车道多边形"""
        if len(shape) < 2:
            return None

        # 使用固定宽度
        half_width = width / 2

        # 创建多边形点
        left_side = []
        right_side = []

        # 处理每个线段
        for i in range(len(shape) - 1):
            p1 = shape[i]
            p2 = shape[i + 1]

            # 计算方向向量
            dx = p2[0] - p1[0]
            dy = p2[1] - p1[1]

            # 计算法向量（垂直于方向向量）
            length = (dx**2 + dy**2)**0.5
            if length > 0:
                nx = -dy / length
                ny = dx / length

                # 计算左侧点
                left_x = p1[0] + nx * half_width
                left_y = p1[1] + ny * half_width
                left_side.append([left_x, left_y])

                # 计算右侧点
                right_x = p1[0] - nx * half_width
                right_y = p1[1] - ny * half_width
                right_side.append([right_x, right_y])

        # 添加最后一个点
        p1 = shape[-2]
        p2 = shape[-1]

        dx = p2[0] - p1[0]
        dy = p2[1] - p1[1]

        length = (dx**2 + dy**2)**0.5
        if length > 0:
            nx = -dy / length
            ny = dx / length

            left_x = p2[0] + nx * half_width
            left_y = p2[1] + ny * half_width
            left_side.append([left_x, left_y])

            right_x = p2[0] - nx * half_width
            right_y = p2[1] - ny * half_width
            right_side.append([right_x, right_y])

        # 组合左右两侧点形成完整多边形
        polygon_points = left_side + list(reversed(right_side))

        # 闭合多边形
        if polygon_points and polygon_points[0] != polygon_points[-1]:
            polygon_points.append(polygon_points[0])

        return polygon_points
    
    def parse_optimized_signal_config(self, sim_id: str) -> Dict[str, Any]:
        """
        解析optimized_signal.add.xml文件，生成信号配时的自然语言描述
        
        参数:
        sim_id: str - 仿真ID
        
        返回:
        Dict[str, Any] - 包含每个交叉口信号配时描述的字典
        """
        signal_file = os.path.join(self.base_dir, sim_id, 'optimized_signal.add.xml')
        
        # 检查文件是否存在
        if not os.path.exists(signal_file):
            return {}
        
        try:
            # 解析XML文件
            tree = ET.parse(signal_file)
            root = tree.getroot()
            
            tls_configs = {}
            
            # 遍历所有tlLogic元素
            for tl_logic in root.findall('tlLogic'):
                tl_id = tl_logic.get('id')
                if not tl_id:
                    continue
                
                phases = tl_logic.findall('phase')
                if len(phases) < 3:  # 至少需要3个phase才能解析
                    continue
                
                north_south_green = None
                north_south_yellow = None
                east_west_green = None
                east_west_yellow = None
                
                if phases:
                    north_south_green = int(phases[0].get('duration', 0))
                
                found_ns_yellow = False
                found_ew_green = False
                
                for i, phase in enumerate(phases[1:], 1):
                    duration = int(phase.get('duration', 0))
                    
                    if not found_ns_yellow and duration == 4:
                        north_south_yellow = duration
                        found_ns_yellow = True
                        continue
                    
                    if found_ns_yellow and not found_ew_green and duration != 4:
                        east_west_green = duration
                        found_ew_green = True
                        continue
                    
                    if found_ew_green and duration == 4:
                        east_west_yellow = duration
                        break
                
                if east_west_yellow is None:
                    east_west_yellow = 4
                
                # 生成描述
                if all(x is not None for x in [north_south_green, north_south_yellow, east_west_green, east_west_yellow]):
                    description = f"南北绿灯{north_south_green}s，南北黄灯{north_south_yellow}s，东西绿灯{east_west_green}s，东西黄灯{east_west_yellow}s"
                else:
                    description = "配时解析失败"
                
                tls_configs[tl_id] = {
                    "description": description
                }
            
            return tls_configs
            
        except Exception as e:
            print(f"解析信号配时文件失败: {str(e)}")
            return {}