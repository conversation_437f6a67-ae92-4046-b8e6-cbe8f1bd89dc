#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试路径问题
"""

import os
import sys
import json

# 添加backend目录到路径
sys.path.append('backend')

from history_manager import HistoryManager

def debug_path():
    """调试路径问题"""
    print("=== 调试路径问题 ===\n")
    
    # 创建历史管理器
    history_manager = HistoryManager()
    
    print(f"当前工作目录: {os.getcwd()}")
    print(f"历史目录: {history_manager.history_dir}")
    print(f"历史文件: {history_manager.history_file}")
    
    # 检查文件是否存在
    print(f"\n文件存在性检查:")
    print(f"历史文件存在: {os.path.exists(history_manager.history_file)}")
    
    # 检查绝对路径
    abs_history_file = os.path.abspath(history_manager.history_file)
    print(f"历史文件绝对路径: {abs_history_file}")
    print(f"绝对路径文件存在: {os.path.exists(abs_history_file)}")
    
    # 尝试读取
    print(f"\n尝试读取历史文件:")
    try:
        with open(history_manager.history_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ 成功读取 {len(data)} 个方案")
        for scheme in data:
            print(f"   - {scheme['id']}: {scheme['name']}")
    except Exception as e:
        print(f"❌ 读取失败: {e}")
    
    # 检查_load_history_data方法
    print(f"\n使用_load_history_data方法:")
    try:
        method_data = history_manager._load_history_data()
        print(f"✅ 方法读取成功 {len(method_data)} 个方案")
        for scheme in method_data:
            print(f"   - {scheme['id']}: {scheme['name']}")
    except Exception as e:
        print(f"❌ 方法读取失败: {e}")

if __name__ == "__main__":
    debug_path()
