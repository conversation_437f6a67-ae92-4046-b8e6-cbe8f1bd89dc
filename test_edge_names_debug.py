#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试路段名称获取功能
"""

import sys
import os
sys.path.append('backend')

from config_parser import ConfigParser
import json

def test_edge_names_debug():
    """调试路段名称获取功能"""
    
    print("=== 调试路段名称获取功能 ===")
    
    parser = ConfigParser()
    
    # 测试路段ID
    test_edge_ids = ['1192690704#17', '980398775#1', '-E11.15', '-980398780#1.304', '1001665911#5']
    
    print(f"模板目录: {parser.template_dir}")
    
    # 检查XML文件是否存在
    net_file = os.path.join(parser.template_dir, 'gym_tls.net.xml')
    print(f"路网文件路径: {net_file}")
    print(f"路网文件存在: {os.path.exists(net_file)}")
    
    if os.path.exists(net_file):
        print(f"文件大小: {os.path.getsize(net_file)} bytes")
    
    # 测试获取路段名称
    print("\n=== 测试路段名称获取 ===")
    edge_names = parser._get_edge_names_from_ids(test_edge_ids)
    
    for edge_id, edge_name in zip(test_edge_ids, edge_names):
        print(f"  {edge_id} -> {edge_name}")
    
    # 测试交叉口名称获取
    print("\n=== 测试交叉口名称获取 ===")
    test_intersection_ids = [
        'cluster_11363432410_11363432411_11513405879_11513405880',
        '10309271185',
        'cluster_11155676766_11155687957'
    ]
    
    intersection_names = parser._get_intersection_names_from_ids(test_intersection_ids)
    
    for intersection_id, intersection_name in zip(test_intersection_ids, intersection_names):
        print(f"  {intersection_id} -> {intersection_name}")
    
    # 测试完整的配置摘要生成
    print("\n=== 测试完整配置摘要生成 ===")
    test_config = {
        'network_config': {
            'type': 'predefined',
            'entrance_plan': '仅开放东侧出入口',
            'road_restriction': {
                'enabled': True,
                'vehicle_restricted_edges': test_edge_ids[:3],
                'pedestrian_restricted_edges': test_edge_ids[3:]
            }
        },
        'signal_config': {
            'type': 'predefined',
            'optimization': {
                'enabled': True,
                'selected_intersections': test_intersection_ids
            }
        },
        'traffic_config': {
            'type': 'predefined',
            'scenario': '进场',
            'vehicle_type': '仅一般车辆',
            'traffic_scale': 'medium'
        }
    }
    
    summary = parser.generate_config_summary(test_config)
    
    print('生成的配置摘要:')
    print(f'  网络: {summary["network"]}')
    print(f'  信号: {summary["signal"]}')
    print(f'  交通: {summary["traffic"]}')

if __name__ == '__main__':
    test_edge_names_debug()
