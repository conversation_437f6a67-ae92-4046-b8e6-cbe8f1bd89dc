{"simulation_id": "250801163628", "name": "测试方案_多道路多交叉口", "description": "包含多个道路限行和交叉口优化的测试方案", "config": {"network_config": {"type": "predefined", "file_path": null, "entrance_plan": "仅开放东侧出入口", "road_restriction": {"enabled": true, "vehicle_restricted_edges": ["1192690704#17", "*********#1", "-E11.15", "1083918697"], "pedestrian_restricted_edges": ["-980398780#1.304", "1001665911#5", "980398774#1"], "vehicle_restricted_edges_with_names": [{"id": "1192690704#17", "name": "易城街"}, {"id": "*********#1", "name": "*********#1"}, {"id": "-E11.15", "name": "-E11.15"}, {"id": "1083918697", "name": "荣乌高速"}], "pedestrian_restricted_edges_with_names": [{"id": "-980398780#1.304", "name": "锦朋路"}, {"id": "1001665911#5", "name": "1001665911#5"}, {"id": "980398774#1", "name": "海岳大街"}]}}, "signal_config": {"type": "predefined", "file_path": null, "optimization": {"enabled": true, "selected_intersections": ["cluster_11363432410_11363432411_11513405879_11513405880", "10309271185", "cluster_11155676766_11155687957", "cluster_11411026127_11411026128"], "selected_intersections_with_names": [{"id": "cluster_11363432410_11363432411_11513405879_11513405880", "name": "交叉口11363432"}, {"id": "10309271185", "name": "交叉口10309271185"}, {"id": "cluster_11155676766_11155687957", "name": "交叉口11155676"}, {"id": "cluster_11411026127_11411026128", "name": "交叉口11411026"}]}}, "traffic_config": {"type": "predefined", "file_path": null, "scenario": "进场", "vehicle_type": "存在贵宾专车", "traffic_scale": "large", "vip_priority": {"enabled": true}}}, "config_summary": {"network": "预设路网 - 仅开放东侧出入口 + 道路限行(易城街、*********#1、-E11.15、荣乌高速、锦朋路、1001665911#5、海岳大街)", "signal": "预设配时 + 自定义优化(交叉口11363432、交叉口10309271185、交叉口11155676、交叉口11411026)", "traffic": "预设需求 - 进场场景 + 大规模 + 贵宾专车(优先通行)"}, "start_time": "2025-08-01T16:36:28.123456", "simulation_results": {"network_metrics": {"pedestrian_metrics": {"average_travel_time": 45.2, "average_waiting_time": 1.8, "average_waiting_count": 0.2, "average_time_loss": 8.5}, "vehicle_metrics": {"average_travel_time": 285.6, "average_waiting_time": 92.3, "average_waiting_count": 5.1, "average_time_loss": 165.4}, "vip_vehicle_metrics": {"average_travel_time": 180.2, "average_waiting_time": 15.6, "average_waiting_count": 1.2, "average_time_loss": 45.8}, "venue_area_metrics": {"average_pedestrian_travel_time": 0, "average_pedestrian_delay": 0, "average_vehicle_travel_time": 0, "average_vehicle_delay": 0}}}}