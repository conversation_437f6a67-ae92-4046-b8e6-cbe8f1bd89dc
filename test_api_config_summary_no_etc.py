#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试API返回的配置摘要是否不包含"等"字样
"""

import requests
import json

def test_api_config_summary_no_etc():
    """测试API返回的配置摘要"""
    
    print("=== 测试API配置摘要（不包含等字样） ===")
    
    # 创建包含多个道路和交叉口的测试配置
    test_config = {
        'network_config': {
            'type': 'predefined',
            'entrance_plan': '仅开放东侧出入口',
            'road_restriction': {
                'enabled': True,
                'vehicle_restricted_edges': [
                    '1192690704#17',  # 易城街
                    '980398775#1',    # 无名称
                    '-E11.15'         # 无名称
                ],
                'pedestrian_restricted_edges': [
                    '-980398780#1.304',  # 锦朋路
                    '1001665911#5'       # 无名称
                ]
            }
        },
        'signal_config': {
            'type': 'predefined',
            'optimization': {
                'enabled': True,
                'selected_intersections': [
                    'cluster_11363432410_11363432411_11513405879_11513405880',  # 交叉口11363432
                    '10309271185',                                              # 交叉口10309271185
                    'cluster_11155676766_11155687957'                           # 交叉口11155676
                ]
            }
        },
        'traffic_config': {
            'type': 'predefined',
            'scenario': '进场',
            'vehicle_type': '仅一般车辆',
            'traffic_scale': 'medium'
        }
    }
    
    try:
        # 发送请求到API
        response = requests.post('http://localhost:8888/api/start_simulation',
                               json=test_config, 
                               timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print("✅ API调用成功")
                
                # 检查返回的配置摘要
                config_summary = result['data'].get('config_summary', {})
                
                print("\nAPI返回的配置摘要:")
                for key, value in config_summary.items():
                    print(f"  {key}: {value}")
                
                # 检查是否包含"等"字样
                print("\n=== 检查省略字样 ===")
                has_etc = False
                for key, value in config_summary.items():
                    if '等' in value:
                        print(f"⚠️  {key}字段包含'等'字样: {value}")
                        has_etc = True
                    else:
                        print(f"✅ {key}字段不包含'等'字样")
                
                if not has_etc:
                    print("\n🎉 所有字段都不包含'等'字样，修改成功！")
                
                # 检查具体道路名和交叉口名
                print("\n=== 检查具体名称 ===")
                network_summary = config_summary.get('network', '')
                signal_summary = config_summary.get('signal', '')
                
                if '易城街' in network_summary or '锦朋路' in network_summary:
                    print("✅ 网络摘要包含具体道路名称")
                else:
                    print("⚠️  网络摘要未包含具体道路名称")
                
                if '交叉口' in signal_summary:
                    print("✅ 信号摘要包含具体交叉口名称")
                else:
                    print("⚠️  信号摘要未包含具体交叉口名称")
                    
            else:
                print(f"❌ API返回错误: {result.get('message', '未知错误')}")
        else:
            print(f"❌ API请求失败，状态码: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器，请确保后端服务正在运行")
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")

if __name__ == '__main__':
    test_api_config_summary_no_etc()
