#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查历史文件
"""

import json

def check_history():
    """检查历史文件"""
    print("=== 检查历史文件 ===\n")
    
    # 读取历史文件
    with open('backend/sumo_data/history/saved_schemes.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"总方案数: {len(data)}")
    
    for i, scheme in enumerate(data, 1):
        print(f"{i}. ID: {scheme['id']}")
        print(f"   名称: {scheme['name']}")
        print(f"   描述: {scheme['description']}")
        print(f"   时间: {scheme['created_time']}")
        print()

if __name__ == "__main__":
    check_history()
