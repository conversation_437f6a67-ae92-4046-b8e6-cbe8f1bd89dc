#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并历史文件
"""

import json
import os
from datetime import datetime

def merge_history():
    """合并历史文件"""
    print("=== 合并历史文件 ===\n")
    
    # 读取两个历史文件
    file1 = 'backend/sumo_data/history/saved_schemes.json'
    file2 = 'sumo_data/history/saved_schemes.json'
    
    data1 = []
    data2 = []
    
    # 读取第一个文件
    if os.path.exists(file1):
        with open(file1, 'r', encoding='utf-8') as f:
            data1 = json.load(f)
        print(f"文件1 ({file1}): {len(data1)} 个方案")
        for scheme in data1:
            print(f"   - {scheme['id']}: {scheme['name']}")
    else:
        print(f"文件1不存在: {file1}")
    
    # 读取第二个文件
    if os.path.exists(file2):
        with open(file2, 'r', encoding='utf-8') as f:
            data2 = json.load(f)
        print(f"\n文件2 ({file2}): {len(data2)} 个方案")
        for scheme in data2:
            print(f"   - {scheme['id']}: {scheme['name']}")
    else:
        print(f"文件2不存在: {file2}")
    
    # 合并数据，去重
    merged_data = []
    scheme_ids = set()
    
    # 先添加文件1的数据（优先级更高）
    for scheme in data1:
        scheme_id = scheme['id']
        if scheme_id not in scheme_ids:
            merged_data.append(scheme)
            scheme_ids.add(scheme_id)
    
    # 再添加文件2的数据（只添加不重复的）
    for scheme in data2:
        scheme_id = scheme['id']
        if scheme_id not in scheme_ids:
            merged_data.append(scheme)
            scheme_ids.add(scheme_id)
    
    # 按时间排序
    merged_data.sort(key=lambda x: x['created_time'], reverse=True)
    
    print(f"\n合并后: {len(merged_data)} 个方案")
    for scheme in merged_data:
        print(f"   - {scheme['id']}: {scheme['name']}")
    
    # 保存合并后的数据到统一位置
    target_file = 'sumo_data/history/saved_schemes.json'
    
    # 确保目录存在
    os.makedirs(os.path.dirname(target_file), exist_ok=True)
    
    # 保存合并后的数据
    with open(target_file, 'w', encoding='utf-8') as f:
        json.dump(merged_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 合并完成，保存到: {target_file}")
    
    # 备份原文件
    if os.path.exists(file1):
        backup_file1 = f"{file1}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.rename(file1, backup_file1)
        print(f"原文件1已备份到: {backup_file1}")

if __name__ == "__main__":
    merge_history()
