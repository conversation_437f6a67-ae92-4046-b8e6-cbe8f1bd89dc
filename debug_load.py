#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试加载问题
"""

import os
import sys
import json

# 添加backend目录到路径
sys.path.append('backend')

from history_manager import HistoryManager

def debug_load():
    """调试加载问题"""
    print("=== 调试加载问题 ===\n")
    
    # 创建历史管理器
    history_manager = HistoryManager()
    
    print(f"历史文件路径: {history_manager.history_file}")
    print(f"仿真结果目录: {history_manager.simulation_results_dir}")
    
    # 直接读取历史文件
    print(f"\n1. 直接读取历史文件:")
    try:
        with open(history_manager.history_file, 'r', encoding='utf-8') as f:
            direct_data = json.load(f)
        print(f"直接读取到 {len(direct_data)} 个方案")
        for i, scheme in enumerate(direct_data[:3], 1):
            print(f"   {i}. {scheme['id']}: {scheme['name']}")
    except Exception as e:
        print(f"❌ 直接读取失败: {e}")
    
    # 使用方法读取历史文件
    print(f"\n2. 使用_load_history_data方法读取:")
    try:
        method_data = history_manager._load_history_data()
        print(f"方法读取到 {len(method_data)} 个方案")
        for i, scheme in enumerate(method_data[:3], 1):
            print(f"   {i}. {scheme['id']}: {scheme['name']}")
    except Exception as e:
        print(f"❌ 方法读取失败: {e}")
    
    # 检查特定ID
    target_id = "250804_112035"
    print(f"\n3. 查找特定ID {target_id}:")
    
    # 在直接读取的数据中查找
    found_direct = False
    for scheme in direct_data:
        if scheme['id'] == target_id:
            print(f"✅ 在直接读取数据中找到: {scheme['name']}")
            found_direct = True
            break
    if not found_direct:
        print(f"❌ 在直接读取数据中未找到")
    
    # 在方法读取的数据中查找
    found_method = False
    for scheme in method_data:
        if scheme['id'] == target_id:
            print(f"✅ 在方法读取数据中找到: {scheme['name']}")
            found_method = True
            break
    if not found_method:
        print(f"❌ 在方法读取数据中未找到")
    
    # 检查数据是否一致
    print(f"\n4. 数据一致性检查:")
    if len(direct_data) == len(method_data):
        print("✅ 数据长度一致")
    else:
        print(f"❌ 数据长度不一致: 直接读取{len(direct_data)}, 方法读取{len(method_data)}")
    
    # 检查ID列表
    direct_ids = [scheme['id'] for scheme in direct_data]
    method_ids = [scheme['id'] for scheme in method_data]
    
    if direct_ids == method_ids:
        print("✅ ID列表一致")
    else:
        print("❌ ID列表不一致")
        print(f"直接读取的ID: {direct_ids[:5]}")
        print(f"方法读取的ID: {method_ids[:5]}")

if __name__ == "__main__":
    debug_load()
