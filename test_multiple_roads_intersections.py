#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试多个道路和交叉口的配置摘要生成
"""

import sys
import os
sys.path.append('backend')

from config_parser import ConfigParser
import json

def test_multiple_roads_intersections():
    """测试包含多个道路和交叉口的配置摘要生成"""
    
    print("=== 测试多个道路和交叉口的配置摘要生成 ===")
    
    # 创建包含多个道路和交叉口的测试配置
    test_config = {
        'network_config': {
            'type': 'predefined',
            'entrance_plan': '仅开放东侧出入口',
            'road_restriction': {
                'enabled': True,
                'vehicle_restricted_edges': [
                    '1192690704#17',  # 易城街
                    '980398775#1',    # 无名称
                    '-E11.15',        # 无名称
                    '1083918697'      # 荣乌高速
                ],
                'pedestrian_restricted_edges': [
                    '-980398780#1.304',  # 锦朋路
                    '1001665911#5',      # 无名称
                    '980398774#1'        # 海岳大街
                ]
            }
        },
        'signal_config': {
            'type': 'predefined',
            'optimization': {
                'enabled': True,
                'selected_intersections': [
                    'cluster_11363432410_11363432411_11513405879_11513405880',  # 交叉口11363432
                    '10309271185',                                              # 交叉口10309271185
                    'cluster_11155676766_11155687957',                          # 交叉口11155676
                    'cluster_11411026127_11411026128'                           # 交叉口11411026
                ]
            }
        },
        'traffic_config': {
            'type': 'predefined',
            'scenario': '进场',
            'vehicle_type': '存在贵宾专车',
            'traffic_scale': 'large',
            'vip_priority': {
                'enabled': True
            }
        }
    }
    
    parser = ConfigParser()
    summary = parser.generate_config_summary(test_config)
    
    print('生成的配置摘要:')
    print(f'  网络: {summary["network"]}')
    print(f'  信号: {summary["signal"]}')
    print(f'  交通: {summary["traffic"]}')
    
    # 分析结果
    print('\n=== 分析结果 ===')
    
    # 检查道路限行信息
    network_summary = summary['network']
    print('道路限行信息分析:')
    if '易城街' in network_summary:
        print('✅ 包含易城街')
    if '荣乌高速' in network_summary:
        print('✅ 包含荣乌高速')
    if '锦朋路' in network_summary:
        print('✅ 包含锦朋路')
    if '海岳大街' in network_summary:
        print('✅ 包含海岳大街')
    
    # 检查交叉口优化信息
    signal_summary = summary['signal']
    print('\n交叉口优化信息分析:')
    if '交叉口11363432' in signal_summary:
        print('✅ 包含交叉口11363432')
    if '交叉口10309271185' in signal_summary:
        print('✅ 包含交叉口10309271185')
    if '交叉口11155676' in signal_summary:
        print('✅ 包含交叉口11155676')
    if '交叉口11411026' in signal_summary:
        print('✅ 包含交叉口11411026')
    
    # 检查是否有省略
    print('\n省略检查:')
    for key, value in summary.items():
        if '等' in value:
            print(f'⚠️  {key}字段包含省略: {value}')
        else:
            print(f'✅ {key}字段无省略')
    
    print('\n=== 统计信息 ===')
    # 统计道路数量
    vehicle_roads = len(test_config['network_config']['road_restriction']['vehicle_restricted_edges'])
    pedestrian_roads = len(test_config['network_config']['road_restriction']['pedestrian_restricted_edges'])
    total_roads = vehicle_roads + pedestrian_roads
    print(f'车辆限行路段: {vehicle_roads}条')
    print(f'行人限行路段: {pedestrian_roads}条')
    print(f'总限行路段: {total_roads}条')
    
    # 统计交叉口数量
    intersections = len(test_config['signal_config']['optimization']['selected_intersections'])
    print(f'优化交叉口: {intersections}个')

if __name__ == '__main__':
    test_multiple_roads_intersections()
