#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试配置摘要生成 - 不使用"等"省略
"""

import sys
import os
sys.path.append('backend')

from config_parser import ConfigParser
import json

def test_config_summary_no_etc():
    """测试配置摘要生成，确保显示所有道路名和交叉口名"""
    
    print("=== 测试配置摘要生成（不使用等省略） ===")
    
    # 创建测试配置
    test_config = {
        'network_config': {
            'type': 'predefined',
            'entrance_plan': '仅开放东侧出入口',
            'road_restriction': {
                'enabled': True,
                'vehicle_restricted_edges': ['1192690704#17', '980398775#1', '-E11.15'],
                'pedestrian_restricted_edges': ['-980398780#1.304', '1001665911#5']
            }
        },
        'signal_config': {
            'type': 'predefined',
            'optimization': {
                'enabled': True,
                'selected_intersections': [
                    'cluster_11363432410_11363432411_11513405879_11513405880',
                    '10309271185',
                    'cluster_11155676766_11155687957'
                ]
            }
        },
        'traffic_config': {
            'type': 'predefined',
            'scenario': '进场',
            'vehicle_type': '仅一般车辆',
            'traffic_scale': 'medium'
        }
    }
    
    parser = ConfigParser()
    summary = parser.generate_config_summary(test_config)
    
    print('生成的配置摘要:')
    print(f'  网络: {summary["network"]}')
    print(f'  信号: {summary["signal"]}')
    print(f'  交通: {summary["traffic"]}')
    
    # 检查是否包含"等"字样
    network_has_etc = '等' in summary['network']
    signal_has_etc = '等' in summary['signal']
    
    print('\n=== 检查结果 ===')
    if network_has_etc:
        print('❌ 网络摘要仍包含"等"字样')
    else:
        print('✅ 网络摘要不包含"等"字样')
        
    if signal_has_etc:
        print('❌ 信号摘要仍包含"等"字样')
    else:
        print('✅ 信号摘要不包含"等"字样')
    
    # 检查是否显示了具体的道路名和交叉口名
    print('\n=== 详细分析 ===')
    print('道路限行信息:')
    if '易城街' in summary['network'] or '荣乌高速' in summary['network']:
        print('✅ 包含具体道路名称')
    else:
        print('⚠️  未找到具体道路名称')
        
    print('交叉口优化信息:')
    if '交叉口' in summary['signal']:
        print('✅ 包含交叉口信息')
    else:
        print('⚠️  未找到交叉口信息')

if __name__ == '__main__':
    test_config_summary_no_etc()
