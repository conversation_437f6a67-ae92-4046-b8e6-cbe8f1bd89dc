#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试方案保存、加载和删除功能的完整性
"""

import json
import os
import sys
from datetime import datetime

# 添加backend目录到路径
sys.path.append('backend')

from history_manager import HistoryManager

def test_save_completeness():
    """测试保存功能的完整性"""
    print("=== 测试方案保存功能完整性 ===")
    
    # 创建历史管理器
    history_manager = HistoryManager()
    
    # 测试配置
    test_config = {
        "network_config": {
            "type": "predefined",
            "entrance_plan": "仅开放东侧出入口",
            "road_restriction": {
                "enabled": True,
                "vehicle_restricted_edges": ["edge1", "edge2"],
                "pedestrian_restricted_edges": ["edge3"]
            }
        },
        "signal_config": {
            "type": "predefined",
            "optimization": {
                "enabled": True,
                "selected_intersections": ["int1", "int2"]
            }
        },
        "traffic_config": {
            "type": "predefined",
            "scenario": "进场",
            "vehicle_type": "仅一般车辆",
            "traffic_scale": "medium"
        }
    }
    
    # 测试仿真结果
    test_simulation_results = {
        "network_metrics": {
            "pedestrian_metrics": {
                "average_travel_time": 57.21,
                "average_waiting_time": 0.31,
                "average_waiting_count": 0.0,
                "average_time_loss": 11.34
            },
            "vehicle_metrics": {
                "average_travel_time": 300.54,
                "average_waiting_time": 91.56,
                "average_waiting_count": 4.28,
                "average_time_loss": 151.8
            }
        },
        "selected_edge_metrics": {
            "pedestrian_metrics": {
                "total_departed": 0.0,
                "total_arrived": 0.0,
                "total_entered": 0.0,
                "avg_traveltime": 0,
                "avg_waitingTime": 0,
                "avg_speed": 0.0,
                "avg_timeLoss": 0,
                "avg_occupancy": 0.0
            },
            "vehicle_metrics": {
                "total_departed": 0,
                "total_arrived": 0.0,
                "total_entered": 22.0,
                "avg_traveltime": 6.525,
                "avg_waitingTime": 0.7272727272727273,
                "avg_speed": 12.6,
                "avg_timeLoss": 3.9395454545454545,
                "avg_occupancy": 0.0098
            }
        }
    }
    
    # 保存方案
    print("1. 保存测试方案...")
    save_result = history_manager.save_scheme(
        config=test_config,
        name="测试方案_完整性验证",
        description="用于验证保存功能完整性的测试方案",
        simulation_results=test_simulation_results
    )
    
    if not save_result['success']:
        print(f"❌ 保存失败: {save_result['error']}")
        return False
    
    scheme_id = save_result['scheme_id']
    print(f"✅ 保存成功，方案ID: {scheme_id}")
    
    # 验证保存的文件
    print("\n2. 验证保存的文件...")
    
    # 检查历史方案文件
    scheme_file = os.path.join('sumo_data', 'history', f'scheme_{scheme_id}.json')
    if os.path.exists(scheme_file):
        print(f"✅ 历史方案文件存在: {scheme_file}")
        
        # 读取并验证内容
        with open(scheme_file, 'r', encoding='utf-8') as f:
            saved_data = json.load(f)
        
        # 验证关键字段
        required_fields = ['simulation_id', 'config', 'config_summary', 'start_time', 'simulation_results']
        for field in required_fields:
            if field in saved_data:
                print(f"✅ 字段 {field} 存在")
            else:
                print(f"❌ 字段 {field} 缺失")
                return False
        
        # 验证selected_edge_metrics
        if 'selected_edge_metrics' in saved_data.get('simulation_results', {}):
            print("✅ selected_edge_metrics 存在")
        else:
            print("❌ selected_edge_metrics 缺失")
            return False
            
    else:
        print(f"❌ 历史方案文件不存在: {scheme_file}")
        return False
    
    # 检查仿真结果文件
    activity_file = os.path.join('backend', 'sumo', 'output', '2023', 'activity', f'simulation_result_{scheme_id}.json')
    if os.path.exists(activity_file):
        print(f"✅ 仿真结果文件存在: {activity_file}")
    else:
        print(f"❌ 仿真结果文件不存在: {activity_file}")
        return False
    
    # 测试加载功能
    print("\n3. 测试加载功能...")
    loaded_scheme = history_manager.get_scheme_by_id(scheme_id)
    
    if loaded_scheme:
        print("✅ 加载成功")
        
        # 验证加载的数据完整性
        if 'config' in loaded_scheme and 'simulation_results' in loaded_scheme:
            print("✅ 配置和仿真结果都存在")
            
            # 验证selected_edge_metrics
            if 'selected_edge_metrics' in loaded_scheme['simulation_results']:
                print("✅ 加载的selected_edge_metrics完整")
            else:
                print("❌ 加载的selected_edge_metrics不完整")
                return False
        else:
            print("❌ 加载的数据不完整")
            return False
    else:
        print("❌ 加载失败")
        return False
    
    # 测试删除功能
    print("\n4. 测试删除功能...")
    delete_result = history_manager.delete_scheme(scheme_id)
    
    if delete_result['success']:
        print("✅ 删除成功")
        
        # 验证文件是否被删除
        if not os.path.exists(scheme_file):
            print("✅ 历史方案文件已删除")
        else:
            print("❌ 历史方案文件未删除")
            return False
            
        if not os.path.exists(activity_file):
            print("✅ 仿真结果文件已删除")
        else:
            print("❌ 仿真结果文件未删除")
            return False
            
        # 验证无法再次加载
        reloaded_scheme = history_manager.get_scheme_by_id(scheme_id)
        if reloaded_scheme is None:
            print("✅ 删除后无法重新加载，符合预期")
        else:
            print("❌ 删除后仍能重新加载，不符合预期")
            return False
    else:
        print(f"❌ 删除失败: {delete_result['error']}")
        return False
    
    print("\n🎉 所有测试通过！方案保存、加载和删除功能完整。")
    return True

def test_existing_schemes():
    """测试现有方案的完整性"""
    print("\n=== 测试现有方案完整性 ===")
    
    history_manager = HistoryManager()
    schemes = history_manager.get_schemes_list()
    
    print(f"发现 {len(schemes)} 个方案")
    
    for i, scheme in enumerate(schemes[:3]):  # 只测试前3个
        print(f"\n测试方案 {i+1}: {scheme['name']} (ID: {scheme['id']})")
        
        # 加载详细数据
        detailed_scheme = history_manager.get_scheme_by_id(scheme['id'])
        
        if detailed_scheme:
            # 验证关键字段
            has_config = 'config' in detailed_scheme
            has_simulation_results = 'simulation_results' in detailed_scheme
            has_selected_edge_metrics = 'selected_edge_metrics' in detailed_scheme.get('simulation_results', {})
            
            print(f"  配置信息: {'✅' if has_config else '❌'}")
            print(f"  仿真结果: {'✅' if has_simulation_results else '❌'}")
            print(f"  选定路段指标: {'✅' if has_selected_edge_metrics else '❌'}")
            
            if not all([has_config, has_simulation_results, has_selected_edge_metrics]):
                print(f"  ⚠️  方案 {scheme['id']} 数据不完整")
        else:
            print(f"  ❌ 无法加载方案 {scheme['id']}")

if __name__ == "__main__":
    try:
        # 测试保存功能完整性
        success = test_save_completeness()
        
        if success:
            # 测试现有方案
            test_existing_schemes()
        else:
            print("❌ 保存功能测试失败，跳过现有方案测试")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
