#!/usr/bin/env python3
"""
测试保存配置功能，验证保存的格式是否与backend\sumo\output\2023\activity目录一致
"""

import requests
import json
import os
from datetime import datetime

def test_save_config():
    """测试保存配置功能"""
    
    # 测试配置数据
    test_config = {
        "network_config": {
            "type": "predefined",
            "file_path": None,
            "entrance_plan": "仅开放东侧出入口",
            "road_restriction": {
                "enabled": True,
                "vehicle_restricted_edges": ["-1307189273#2"],
                "pedestrian_restricted_edges": ["-1307189273#2"],
                "vehicle_restricted_edges_with_names": [
                    {"id": "-1307189273#2", "name": "-1307189273#2"}
                ],
                "pedestrian_restricted_edges_with_names": [
                    {"id": "-1307189273#2", "name": "-1307189273#2"}
                ]
            }
        },
        "signal_config": {
            "type": "predefined",
            "file_path": None,
            "optimization": {
                "enabled": True,
                "selected_intersections": ["cluster_11471925738_11471925739"],
                "selected_intersections_with_names": [
                    {"id": "cluster_11471925738_11471925739", "name": "交叉口11471925"}
                ]
            }
        },
        "traffic_config": {
            "type": "predefined",
            "file_path": None,
            "scenario": "进场",
            "vehicle_type": "仅一般车辆",
            "traffic_scale": "medium",
            "vip_priority": {"enabled": False}
        },
        "analysis_config": {
            "edge_analysis": {
                "enabled": True,
                "selected_edges": ["-1005075672#10"],
                "selected_edges_with_names": [
                    {"id": "-1005075672#10", "name": "易宁大街"}
                ]
            }
        }
    }
    
    # 测试仿真结果数据
    test_simulation_results = {
        "network_metrics": {
            "pedestrian_metrics": {
                "average_travel_time": 54.23,
                "average_waiting_time": 0.05,
                "average_waiting_count": 0.0,
                "average_time_loss": 8.36
            },
            "vehicle_metrics": {
                "average_travel_time": 300.49,
                "average_waiting_time": 83.43,
                "average_waiting_count": 4.23,
                "average_time_loss": 147.5
            },
            "vip_vehicle_metrics": {
                "average_travel_time": 0,
                "average_waiting_time": 0,
                "average_waiting_count": 0,
                "average_time_loss": 0
            },
            "venue_area_metrics": {
                "average_pedestrian_travel_time": 0,
                "average_pedestrian_delay": 0,
                "average_vehicle_travel_time": 0,
                "average_vehicle_delay": 0
            }
        },
        "selected_edge_metrics": {
            "pedestrian_metrics": {
                "total_departed": 0.0,
                "total_arrived": 0.0,
                "total_entered": 0.0,
                "avg_traveltime": 0,
                "avg_waitingTime": 0,
                "avg_speed": 0.0,
                "avg_timeLoss": 0,
                "avg_occupancy": 0.0
            },
            "vehicle_metrics": {
                "total_departed": 0,
                "total_arrived": 2.0,
                "total_entered": 52.0,
                "avg_traveltime": 6.075000000000001,
                "avg_waitingTime": 2.519230769230769,
                "avg_speed": 9.21,
                "avg_timeLoss": 4.884615384615385,
                "avg_occupancy": 0.0273
            }
        }
    }
    
    # 发送保存请求
    url = "http://localhost:8888/api/history/save"
    data = {
        "config": test_config,
        "name": "测试保存格式一致性",
        "description": "测试保存的格式是否与activity目录一致",
        "simulation_results": test_simulation_results
    }
    
    print("发送保存请求...")
    response = requests.post(url, json=data)
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            scheme_id = result['scheme_id']
            print(f"✅ 保存成功，方案ID: {scheme_id}")
            
            # 检查保存的文件格式
            check_saved_format(scheme_id)
        else:
            print(f"❌ 保存失败: {result['error']}")
    else:
        print(f"❌ 请求失败，状态码: {response.status_code}")
        print(f"响应内容: {response.text}")

def check_saved_format(scheme_id):
    """检查保存的文件格式"""
    
    # 检查history目录中的文件
    history_file = f"backend/sumo_data/history/scheme_{scheme_id}.json"
    if os.path.exists(history_file):
        print(f"\n📁 检查history目录文件: {history_file}")
        with open(history_file, 'r', encoding='utf-8') as f:
            history_data = json.load(f)
        
        print("History文件结构:")
        for key in history_data.keys():
            print(f"  - {key}")
        
        # 检查是否使用了新格式
        if 'simulation_id' in history_data:
            print("✅ 使用了新格式 (simulation_id)")
        else:
            print("❌ 仍使用旧格式 (id)")
            
        if 'start_time' in history_data:
            print("✅ 使用了新格式 (start_time)")
        else:
            print("❌ 仍使用旧格式 (created_time)")
    
    # 检查activity目录中的文件
    activity_file = f"backend/sumo/output/2023/activity/simulation_result_{scheme_id}.json"
    if os.path.exists(activity_file):
        print(f"\n📁 检查activity目录文件: {activity_file}")
        with open(activity_file, 'r', encoding='utf-8') as f:
            activity_data = json.load(f)
        
        print("Activity文件结构:")
        for key in activity_data.keys():
            print(f"  - {key}")
        
        # 与现有activity文件对比
        existing_file = "backend/sumo/output/2023/activity/simulation_result_250801171330.json"
        if os.path.exists(existing_file):
            with open(existing_file, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
            
            print(f"\n🔍 与现有文件对比 ({existing_file}):")
            existing_keys = set(existing_data.keys())
            new_keys = set(activity_data.keys())
            
            if existing_keys == new_keys:
                print("✅ 顶级字段结构一致")
            else:
                print("❌ 顶级字段结构不一致")
                print(f"  现有文件字段: {existing_keys}")
                print(f"  新保存文件字段: {new_keys}")
                print(f"  缺少字段: {existing_keys - new_keys}")
                print(f"  多余字段: {new_keys - existing_keys}")
    else:
        print(f"❌ Activity目录文件不存在: {activity_file}")

if __name__ == "__main__":
    test_save_config()
